package com.zjjcnt.project.ck.sysadmin.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.zjjcnt.common.core.annotation.Crypto;
import com.zjjcnt.common.core.entity.IdEntity;
import lombok.Data;

/**
 * 公共代码DO
 *
 * <AUTHOR>
 * @date 2024-05-13 16:43:17
 * @see com.zjjcnt.project.ck.zzj.dto.fw.FwtDmGgDTO
 */
@Crypto
@Data
@Table("fwt_dm_gg")
public class FwtDmGgDO implements IdEntity<String> {
    private static final long serialVersionUID = 6026232185291382793L;

    /**
     *
     */
    @Id( keyType = KeyType.None)
    private String dmzm;

    /**
     *
     */
    @Id( keyType = KeyType.None)
    private String dmlx;

    /**
     *
     */
    @Column(value = "dmmc")
    private String dmmc;

    /**
     *
     */
    @Column(value = "dmpy")
    private String dmpy;

    /**
     *
     */
    @Column(value = "dmsx")
    private String dmsx;

    /**
     *
     */
    @Column(value = "yxbz")
    private String yxbz;

    /**
     *
     */
    @Column(value = "xgsj")
    private String xgsj;

    /**
     *
     */
    @Crypto
    @Column(value = "dmlxmc")
    private String dmlxmc;

    /**
     *
     */
    @Column(value = "kzbzb")
    private String kzbzb;

    /**
     *
     */
    @Column(value = "kzbzc")
    private String kzbzc;

    /**
     *
     */
    @Column(value = "kzbzd")
    private String kzbzd;

    /**
     *
     */
    @Column(value = "kzbze")
    private String kzbze;

    /**
     *
     */
    @Column(value = "kzbzf")
    private String kzbzf;

    /**
     *
     */
    @Column(value = "kzbzg")
    private String kzbzg;


    @Override
    public String getId() {
        return this.dmzm;
    }

    @Override
    public void setId(String id) {
        this.dmzm = id;
    }
}
