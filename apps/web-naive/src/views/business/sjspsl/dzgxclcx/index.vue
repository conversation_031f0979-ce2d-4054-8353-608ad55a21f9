<script lang="ts" setup name="user-index">
import { Page, useVbenForm } from '@vben/common-ui';

import { defaultFormConfig } from '#/adapter/form';
import { apiGaggsjGadzgxcl } from '#/api';

import { QueryFormSchema } from './data';

// 定义接口返回数据类型
interface StructDataToImage {
  index: number;
  logWjbh: string;
  base64wj: string;
  orgiUrl: string;
  orgiDownloadWjbh: string;
  structList: any[];
}

interface ApiResponse {
  structDataToImageList: StructDataToImage[];
}

// 响应式数据
const loading = ref(false);
const resultData = ref<StructDataToImage[]>([]);
const activeTab = ref('1');

// 表单配置
const [Form] = useVbenForm({
  schema: QueryFormSchema,
  ...defaultFormConfig,
  handleSubmit: async (values) => {
    await handleQuery(values);
  },
});

// 查询处理函数
const handleQuery = async (formValues: any) => {
  try {
    loading.value = true;
    const { exchangeServiceId, ...requestMap } = formValues;
    const result: ApiResponse = await apiGaggsjGadzgxcl({
      exchangeServiceId,
      requestMap,
    });

    resultData.value = result.structDataToImageList || [];
    // 设置默认选中第一个tab
    if (resultData.value.length > 0) {
      activeTab.value = '1';
    }
  } catch (error) {
    console.error('查询失败:', error);
    resultData.value = [];
  } finally {
    loading.value = false;
  }
};
</script>
<template>
  <Page>
    <!-- 查询表单 -->
    <div class="mb-4">
      <Form />
    </div>

    <!-- 结果展示区域 -->
    <div v-if="resultData.length > 0" class="flex h-full">
      <!-- 左侧Tab导航 -->
      <div class="w-48 border-r border-gray-200">
        <a-tabs
          v-model:active-key="activeTab"
          tab-position="left"
          class="h-full"
        >
          <a-tab-pane
            v-for="(_item, index) in resultData"
            :key="String(index + 1)"
            :tab="String(index + 1)"
          />
        </a-tabs>
      </div>

      <!-- 右侧内容区域 -->
      <div class="flex-1 p-4">
        <div v-for="(item, index) in resultData" :key="index">
          <div v-if="activeTab === String(index + 1)">
            <div v-if="item.base64wj" class="text-center">
              <img
                :src="`data:image/jpeg;base64,${item.base64wj}`"
                :alt="`图片 ${index + 1}`"
                class="h-auto max-w-full rounded border border-gray-300"
                style="max-height: 80vh"
              />
            </div>
            <div v-else class="py-8 text-center text-gray-500">
              暂无图片数据
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="py-8 text-center">
      <a-spin size="large" />
    </div>
  </Page>
</template>
<style lang="scss" scoped></style>
