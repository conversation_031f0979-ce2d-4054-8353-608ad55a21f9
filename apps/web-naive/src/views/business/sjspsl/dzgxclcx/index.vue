<script lang="ts" setup name="user-index">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { Page } from '@vben/common-ui';

import { defaultFormConfig } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { apiGaggsjGadzgxcl } from '#/api';

import { QueryFormSchema } from './data';
// import _Form from './form.vue';
// import _View from './view.vue';

const searchCount = ref(true);

// const handleView = async (row: xtXzqhbPageResp) => {
//   const res = await apiXtXzqhbView({ id: row.dm });
//   viewApi.setData(res).open();
// };
// const handleForm = async (row: null | xtXzqhbPageResp, code: FormType) => {
//   if (row) {
//     const res = await apiXtXzqhbView({ id: row!.dm });
//     formApi.setData({ ...res, FormType: code, gridApi }).open();
//   } else {
//     formApi.setData({ FormType: code }).open();
//   }
// };
// const handleDelete = async (row: xtXzqhbPageResp) => {
//   await apiXtXzqhbDelete([row.dm]);
//   message.success('删除成功');
//   gridApi.query();
// };

// const [View, viewApi] = useVbenDrawer({
//   connectedComponent: _View,
//   destroyOnClose: true,
// });
// const [Form, formApi] = useVbenDrawer({
//   connectedComponent: _Form,
//   destroyOnClose: true,
// });

// const onActionClick = ({ code, row }: OnActionClickParams<xtXzqhbPageResp>) => {
//   switch (code) {
//     case 'delete': {
//       handleDelete(row);
//       break;
//     }
//     case 'edit': {
//       handleForm(row, code);
//       break;
//     }
//     case 'view': {
//       handleView(row);
//       break;
//     }
//     default: {
//       break;
//     }
//   }
// };
const [Grid] = useVbenVxeGrid({
  formOptions: {
    schema: QueryFormSchema,
    ...defaultFormConfig,
  },
  gridOptions: {
    proxyConfig: {
      ajax: {
        query: async (formValues) => {
          const result = await apiGaggsjGadzgxcl({
            ...formValues,
          });
          return result;
        },
      },
    },
    rowConfig: {
      keyField: 'dm',
    },
  } as VxeTableGridOptions,
});
</script>
<template>
  <Page>
    <Grid>
      <template #submit-before>
        <a-switch v-model:checked="searchCount" />
      </template>
    </Grid>
    <View />
    <Form />
  </Page>
</template>
<style lang="scss" scoped></style>
