import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import { apiFwtDmGgListByDmlx } from '#/api';

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      api: apiFwtDmGgListByDmlx,
      params: {
        dmlx: 'dzgxcllx',
      },
      afterFetch: (options: any[]) => {
        if (!options) {
          return [];
        }
        return options.map((item: any) => ({
          text: item.dmmc,
          value: item.kzbzb,
        }));
      },
    },
    fieldName: 'exchangeServiceId',
    label: '业务及材料类型',
  },
];
