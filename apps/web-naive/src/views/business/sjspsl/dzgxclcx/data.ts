import type { BaseFormComponentType, VbenFormSchema } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import pinyin from 'js-pinyin';

import { apiFwtDmGgListByDmlx } from '#/api';

pinyin.setOptions({ charCase: 2, checkPolyphone: false });

// 搜索表单配置
export const QueryFormSchema: VbenFormSchema<BaseFormComponentType>[] = [
  {
    component: 'ApiSelect',
    componentProps: {
      placeholder: '请选择',
      options: [],
      params: {
        api: apiFwtDmGgListByDmlx,
        type: 'dzgxcllx',
      },
      afterFetch: (options: any[]) => {
        console.log(options);
        return options;
        return options.filter((item) => {
          const value = item.value;
          return value > '20' && value < '30';
        });
      },
    },
    fieldName: 'exchangeServiceId',
    label: '业务及材料类型',
  },
];

// 表格列数据
export function useColumns<T = any>(
  onActionClick: T,
): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'dm',
      title: '代码',
    },
    {
      field: 'mc',
      title: '名称',
    },
    {
      field: 'zwpy',
      title: '中文拼音',
    },
    {
      field: 'bz',
      title: '备注',
    },
    {
      field: 'qybzLabel',
      title: '启用标志',
    },
    {
      field: 'isgjLabel',
      title: '是否国家',
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'name',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          'view', // 默认的详情按钮
          'edit', // 默认的编辑按钮
          {
            code: 'delete',
            text: '删除',
            show: (row: any) => row.qybz === '1',
          }, // 默认的删除按钮
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      minWidth: 160,
    },
  ];
}
