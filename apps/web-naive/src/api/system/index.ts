export * from './common';
export * from './fwtDmGg';
export * from './sysMenu';
export * from './xtBssqb';
export * from './xtDwjgdmb';
export * from './xtDwxxb';
export * from './xtJlxxxb';
export * from './xtJsxxb';
export * from './xtJwhxxb';
export * from './xtXtcsb';
export * from './xtXtkzcsb';
export * from './xtXzjdxxb';
export * from './xtXzqhb';
export * from './xtYhxxb';

export type pageQuery<T = any> = T & {
  pageNumber: number;
  pageSize: number;
};

export type pageResp<T = any> = {
  empty: boolean;
  first: boolean;
  last: boolean;
  pageNumber: number;
  pageSize: number;
  records: T[];
  searchCount: boolean;
  total: number;
  totalPages: number;
};
