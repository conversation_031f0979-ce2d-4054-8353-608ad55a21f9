package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.CrjScgjRybDTO;
import com.zjjcnt.project.ck.base.dto.req.CrjScgjRybCreateReq;
import com.zjjcnt.project.ck.base.dto.req.CrjScgjRybPageReq;
import com.zjjcnt.project.ck.base.dto.req.CrjScgjRybUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.CrjScgjRybCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.CrjScgjRybPageResp;
import com.zjjcnt.project.ck.base.dto.resp.CrjScgjRybViewResp;
import com.zjjcnt.project.ck.base.entity.CrjScgjRybDO;
import com.zjjcnt.project.ck.base.exp.CrjScgjRybExp;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:55+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class CrjScgjRybConvertImpl implements CrjScgjRybConvert {

    @Override
    public CrjScgjRybDTO convert(CrjScgjRybDO entity) {
        if ( entity == null ) {
            return null;
        }

        CrjScgjRybDTO crjScgjRybDTO = new CrjScgjRybDTO();

        crjScgjRybDTO.setId( entity.getId() );
        crjScgjRybDTO.setScgjryid( entity.getScgjryid() );
        crjScgjRybDTO.setScgjpcid( entity.getScgjpcid() );
        crjScgjRybDTO.setCzsj( entity.getCzsj() );
        crjScgjRybDTO.setGj( entity.getGj() );
        crjScgjRybDTO.setHzh( entity.getHzh() );
        crjScgjRybDTO.setGmsfhm( entity.getGmsfhm() );
        crjScgjRybDTO.setXm( entity.getXm() );
        crjScgjRybDTO.setHjsjly( entity.getHjsjly() );
        crjScgjRybDTO.setHjdz( entity.getHjdz() );
        crjScgjRybDTO.setHkzt( entity.getHkzt() );
        crjScgjRybDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        crjScgjRybDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        crjScgjRybDTO.setYxbz( entity.getYxbz() );
        crjScgjRybDTO.setClbz( entity.getClbz() );
        crjScgjRybDTO.setRksj( entity.getRksj() );
        crjScgjRybDTO.setSjshbz( entity.getSjshbz() );
        crjScgjRybDTO.setSjshms( entity.getSjshms() );
        crjScgjRybDTO.setSjshrxm( entity.getSjshrxm() );
        crjScgjRybDTO.setSjshsj( entity.getSjshsj() );
        crjScgjRybDTO.setQsrxm( entity.getQsrxm() );
        crjScgjRybDTO.setQssj( entity.getQssj() );
        crjScgjRybDTO.setSpywslh( entity.getSpywslh() );
        crjScgjRybDTO.setYwslsj( entity.getYwslsj() );
        crjScgjRybDTO.setYwslrxm( entity.getYwslrxm() );
        crjScgjRybDTO.setBjsj( entity.getBjsj() );
        crjScgjRybDTO.setBjyy( entity.getBjyy() );
        crjScgjRybDTO.setBz( entity.getBz() );

        return crjScgjRybDTO;
    }

    @Override
    public CrjScgjRybDO convertToDO(CrjScgjRybDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjRybDO crjScgjRybDO = new CrjScgjRybDO();

        crjScgjRybDO.setId( dto.getId() );
        crjScgjRybDO.setScgjryid( dto.getScgjryid() );
        crjScgjRybDO.setScgjpcid( dto.getScgjpcid() );
        crjScgjRybDO.setCzsj( dto.getCzsj() );
        crjScgjRybDO.setGj( dto.getGj() );
        crjScgjRybDO.setHzh( dto.getHzh() );
        crjScgjRybDO.setGmsfhm( dto.getGmsfhm() );
        crjScgjRybDO.setXm( dto.getXm() );
        crjScgjRybDO.setHjsjly( dto.getHjsjly() );
        crjScgjRybDO.setHjdz( dto.getHjdz() );
        crjScgjRybDO.setHkzt( dto.getHkzt() );
        crjScgjRybDO.setSjgsdwdm( dto.getSjgsdwdm() );
        crjScgjRybDO.setSjgsdwmc( dto.getSjgsdwmc() );
        crjScgjRybDO.setYxbz( dto.getYxbz() );
        crjScgjRybDO.setClbz( dto.getClbz() );
        crjScgjRybDO.setRksj( dto.getRksj() );
        crjScgjRybDO.setSjshbz( dto.getSjshbz() );
        crjScgjRybDO.setSjshms( dto.getSjshms() );
        crjScgjRybDO.setSjshrxm( dto.getSjshrxm() );
        crjScgjRybDO.setSjshsj( dto.getSjshsj() );
        crjScgjRybDO.setQsrxm( dto.getQsrxm() );
        crjScgjRybDO.setQssj( dto.getQssj() );
        crjScgjRybDO.setSpywslh( dto.getSpywslh() );
        crjScgjRybDO.setYwslsj( dto.getYwslsj() );
        crjScgjRybDO.setYwslrxm( dto.getYwslrxm() );
        crjScgjRybDO.setBjsj( dto.getBjsj() );
        crjScgjRybDO.setBjyy( dto.getBjyy() );
        crjScgjRybDO.setBz( dto.getBz() );

        return crjScgjRybDO;
    }

    @Override
    public CrjScgjRybDTO convertToDTO(CrjScgjRybPageReq req) {
        if ( req == null ) {
            return null;
        }

        CrjScgjRybDTO crjScgjRybDTO = new CrjScgjRybDTO();

        crjScgjRybDTO.setScgjpcid( req.getScgjpcid() );
        crjScgjRybDTO.setGj( req.getGj() );
        crjScgjRybDTO.setHzh( req.getHzh() );
        crjScgjRybDTO.setGmsfhm( req.getGmsfhm() );
        crjScgjRybDTO.setXm( req.getXm() );
        crjScgjRybDTO.setHkzt( req.getHkzt() );
        crjScgjRybDTO.setYxbz( req.getYxbz() );
        crjScgjRybDTO.setClbz( req.getClbz() );
        crjScgjRybDTO.setCzsjStart( req.getCzsjStart() );
        crjScgjRybDTO.setCzsjEnd( req.getCzsjEnd() );
        crjScgjRybDTO.setRksjStart( req.getRksjStart() );
        crjScgjRybDTO.setRksjEnd( req.getRksjEnd() );

        return crjScgjRybDTO;
    }

    @Override
    public CrjScgjRybDTO convertToDTO(CrjScgjRybCreateReq req) {
        if ( req == null ) {
            return null;
        }

        CrjScgjRybDTO crjScgjRybDTO = new CrjScgjRybDTO();

        crjScgjRybDTO.setScgjpcid( req.getScgjpcid() );
        crjScgjRybDTO.setGj( req.getGj() );
        crjScgjRybDTO.setHzh( req.getHzh() );
        crjScgjRybDTO.setGmsfhm( req.getGmsfhm() );

        return crjScgjRybDTO;
    }

    @Override
    public CrjScgjRybDTO convertToDTO(CrjScgjRybUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        CrjScgjRybDTO crjScgjRybDTO = new CrjScgjRybDTO();

        crjScgjRybDTO.setScgjryid( req.getScgjryid() );
        crjScgjRybDTO.setGj( req.getGj() );
        crjScgjRybDTO.setHzh( req.getHzh() );

        return crjScgjRybDTO;
    }

    @Override
    public CrjScgjRybPageResp convertToPageResp(CrjScgjRybDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjRybPageResp crjScgjRybPageResp = new CrjScgjRybPageResp();

        crjScgjRybPageResp.setScgjryid( dto.getScgjryid() );
        crjScgjRybPageResp.setScgjpcid( dto.getScgjpcid() );
        crjScgjRybPageResp.setCzsj( dto.getCzsj() );
        crjScgjRybPageResp.setGj( dto.getGj() );
        crjScgjRybPageResp.setHzh( dto.getHzh() );
        crjScgjRybPageResp.setGmsfhm( dto.getGmsfhm() );
        crjScgjRybPageResp.setXm( dto.getXm() );
        crjScgjRybPageResp.setHjsjly( dto.getHjsjly() );
        crjScgjRybPageResp.setHjdz( dto.getHjdz() );
        crjScgjRybPageResp.setHkzt( dto.getHkzt() );
        crjScgjRybPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        crjScgjRybPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        crjScgjRybPageResp.setClbz( dto.getClbz() );
        crjScgjRybPageResp.setRksj( dto.getRksj() );
        crjScgjRybPageResp.setSjshbz( dto.getSjshbz() );
        crjScgjRybPageResp.setSjshms( dto.getSjshms() );
        crjScgjRybPageResp.setSjshrxm( dto.getSjshrxm() );
        crjScgjRybPageResp.setSjshsj( dto.getSjshsj() );
        crjScgjRybPageResp.setQsrxm( dto.getQsrxm() );
        crjScgjRybPageResp.setQssj( dto.getQssj() );
        crjScgjRybPageResp.setSpywslh( dto.getSpywslh() );
        crjScgjRybPageResp.setYwslsj( dto.getYwslsj() );
        crjScgjRybPageResp.setYwslrxm( dto.getYwslrxm() );
        crjScgjRybPageResp.setBjsj( dto.getBjsj() );
        crjScgjRybPageResp.setBjyy( dto.getBjyy() );
        crjScgjRybPageResp.setBz( dto.getBz() );

        return crjScgjRybPageResp;
    }

    @Override
    public CrjScgjRybViewResp convertToViewResp(CrjScgjRybDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjRybViewResp crjScgjRybViewResp = new CrjScgjRybViewResp();

        crjScgjRybViewResp.setScgjryid( dto.getScgjryid() );
        crjScgjRybViewResp.setScgjpcid( dto.getScgjpcid() );
        crjScgjRybViewResp.setCzsj( dto.getCzsj() );
        crjScgjRybViewResp.setGj( dto.getGj() );
        crjScgjRybViewResp.setHzh( dto.getHzh() );
        crjScgjRybViewResp.setGmsfhm( dto.getGmsfhm() );
        crjScgjRybViewResp.setXm( dto.getXm() );
        crjScgjRybViewResp.setHjsjly( dto.getHjsjly() );
        crjScgjRybViewResp.setHjdz( dto.getHjdz() );
        crjScgjRybViewResp.setHkzt( dto.getHkzt() );
        crjScgjRybViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        crjScgjRybViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        crjScgjRybViewResp.setClbz( dto.getClbz() );
        crjScgjRybViewResp.setRksj( dto.getRksj() );
        crjScgjRybViewResp.setSjshbz( dto.getSjshbz() );
        crjScgjRybViewResp.setSjshms( dto.getSjshms() );
        crjScgjRybViewResp.setSjshrxm( dto.getSjshrxm() );
        crjScgjRybViewResp.setSjshsj( dto.getSjshsj() );
        crjScgjRybViewResp.setQsrxm( dto.getQsrxm() );
        crjScgjRybViewResp.setQssj( dto.getQssj() );
        crjScgjRybViewResp.setSpywslh( dto.getSpywslh() );
        crjScgjRybViewResp.setYwslsj( dto.getYwslsj() );
        crjScgjRybViewResp.setYwslrxm( dto.getYwslrxm() );
        crjScgjRybViewResp.setBjsj( dto.getBjsj() );
        crjScgjRybViewResp.setBjyy( dto.getBjyy() );
        crjScgjRybViewResp.setBz( dto.getBz() );

        return crjScgjRybViewResp;
    }

    @Override
    public CrjScgjRybCreateResp convertToCreateResp(CrjScgjRybDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjRybCreateResp crjScgjRybCreateResp = new CrjScgjRybCreateResp();

        crjScgjRybCreateResp.setScgjryid( dto.getScgjryid() );
        crjScgjRybCreateResp.setScgjpcid( dto.getScgjpcid() );
        crjScgjRybCreateResp.setCzsj( dto.getCzsj() );
        crjScgjRybCreateResp.setGj( dto.getGj() );
        crjScgjRybCreateResp.setHzh( dto.getHzh() );
        crjScgjRybCreateResp.setGmsfhm( dto.getGmsfhm() );
        crjScgjRybCreateResp.setXm( dto.getXm() );
        crjScgjRybCreateResp.setHjsjly( dto.getHjsjly() );
        crjScgjRybCreateResp.setHjdz( dto.getHjdz() );
        crjScgjRybCreateResp.setHkzt( dto.getHkzt() );
        crjScgjRybCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        crjScgjRybCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        crjScgjRybCreateResp.setClbz( dto.getClbz() );
        crjScgjRybCreateResp.setRksj( dto.getRksj() );
        crjScgjRybCreateResp.setSjshbz( dto.getSjshbz() );
        crjScgjRybCreateResp.setSjshms( dto.getSjshms() );
        crjScgjRybCreateResp.setSjshrxm( dto.getSjshrxm() );
        crjScgjRybCreateResp.setSjshsj( dto.getSjshsj() );
        crjScgjRybCreateResp.setQsrxm( dto.getQsrxm() );
        crjScgjRybCreateResp.setQssj( dto.getQssj() );
        crjScgjRybCreateResp.setSpywslh( dto.getSpywslh() );
        crjScgjRybCreateResp.setYwslsj( dto.getYwslsj() );
        crjScgjRybCreateResp.setYwslrxm( dto.getYwslrxm() );
        crjScgjRybCreateResp.setBjsj( dto.getBjsj() );
        crjScgjRybCreateResp.setBjyy( dto.getBjyy() );
        crjScgjRybCreateResp.setBz( dto.getBz() );

        return crjScgjRybCreateResp;
    }

    @Override
    public CrjScgjRybExp convertToExp(CrjScgjRybDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjRybExp crjScgjRybExp = new CrjScgjRybExp();

        crjScgjRybExp.setScgjryid( dto.getScgjryid() );
        crjScgjRybExp.setScgjpcid( dto.getScgjpcid() );
        crjScgjRybExp.setCzsj( dto.getCzsj() );
        crjScgjRybExp.setGj( dto.getGj() );
        crjScgjRybExp.setHzh( dto.getHzh() );
        crjScgjRybExp.setGmsfhm( dto.getGmsfhm() );
        crjScgjRybExp.setXm( dto.getXm() );
        crjScgjRybExp.setHjsjly( dto.getHjsjly() );
        crjScgjRybExp.setHjdz( dto.getHjdz() );
        crjScgjRybExp.setHkzt( dto.getHkzt() );
        crjScgjRybExp.setSjgsdwdm( dto.getSjgsdwdm() );
        crjScgjRybExp.setSjgsdwmc( dto.getSjgsdwmc() );
        crjScgjRybExp.setYxbz( dto.getYxbz() );
        crjScgjRybExp.setClbz( dto.getClbz() );
        crjScgjRybExp.setRksj( dto.getRksj() );
        crjScgjRybExp.setSjshbz( dto.getSjshbz() );
        crjScgjRybExp.setSjshms( dto.getSjshms() );
        crjScgjRybExp.setSjshrxm( dto.getSjshrxm() );
        crjScgjRybExp.setSjshsj( dto.getSjshsj() );
        crjScgjRybExp.setQsrxm( dto.getQsrxm() );
        crjScgjRybExp.setQssj( dto.getQssj() );
        crjScgjRybExp.setSpywslh( dto.getSpywslh() );
        crjScgjRybExp.setYwslsj( dto.getYwslsj() );
        crjScgjRybExp.setYwslrxm( dto.getYwslrxm() );
        crjScgjRybExp.setBjsj( dto.getBjsj() );
        crjScgjRybExp.setBjyy( dto.getBjyy() );
        crjScgjRybExp.setBz( dto.getBz() );

        return crjScgjRybExp;
    }
}
