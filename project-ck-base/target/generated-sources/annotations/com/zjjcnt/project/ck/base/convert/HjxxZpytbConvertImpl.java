package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxZpytbDTO;
import com.zjjcnt.project.ck.base.entity.HjxxZpytbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxZpytbConvertImpl implements HjxxZpytbConvert {

    @Override
    public HjxxZpytbDTO convert(HjxxZpytbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxZpytbDTO hjxxZpytbDTO = new HjxxZpytbDTO();

        hjxxZpytbDTO.setId( entity.getId() );
        hjxxZpytbDTO.setZpytbid( entity.getZpytbid() );
        hjxxZpytbDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxZpytbDTO.setBcsj( entity.getBcsj() );
        hjxxZpytbDTO.setZpdx( entity.getZpdx() );
        hjxxZpytbDTO.setZpwjlx( entity.getZpwjlx() );
        hjxxZpytbDTO.setZpsjdzlx( entity.getZpsjdzlx() );
        hjxxZpytbDTO.setZpsjdz( entity.getZpsjdz() );
        hjxxZpytbDTO.setZphash( entity.getZphash() );
        hjxxZpytbDTO.setZpcjlx( entity.getZpcjlx() );
        hjxxZpytbDTO.setZpsbbsh( entity.getZpsbbsh() );
        hjxxZpytbDTO.setZpsbppxh( entity.getZpsbppxh() );
        hjxxZpytbDTO.setZpsbppxhdm( entity.getZpsbppxhdm() );
        hjxxZpytbDTO.setBz( entity.getBz() );

        return hjxxZpytbDTO;
    }

    @Override
    public HjxxZpytbDO convertToDO(HjxxZpytbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxZpytbDO hjxxZpytbDO = new HjxxZpytbDO();

        hjxxZpytbDO.setId( dto.getId() );
        hjxxZpytbDO.setZpytbid( dto.getZpytbid() );
        hjxxZpytbDO.setGmsfhm( dto.getGmsfhm() );
        hjxxZpytbDO.setBcsj( dto.getBcsj() );
        hjxxZpytbDO.setZpdx( dto.getZpdx() );
        hjxxZpytbDO.setZpwjlx( dto.getZpwjlx() );
        hjxxZpytbDO.setZpsjdzlx( dto.getZpsjdzlx() );
        hjxxZpytbDO.setZpsjdz( dto.getZpsjdz() );
        hjxxZpytbDO.setZphash( dto.getZphash() );
        hjxxZpytbDO.setZpcjlx( dto.getZpcjlx() );
        hjxxZpytbDO.setZpsbbsh( dto.getZpsbbsh() );
        hjxxZpytbDO.setZpsbppxh( dto.getZpsbppxh() );
        hjxxZpytbDO.setZpsbppxhdm( dto.getZpsbppxhdm() );
        hjxxZpytbDO.setBz( dto.getBz() );

        return hjxxZpytbDO;
    }
}
