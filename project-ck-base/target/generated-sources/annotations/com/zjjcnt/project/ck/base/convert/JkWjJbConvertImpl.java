package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.JkWjJbDTO;
import com.zjjcnt.project.ck.base.entity.JkWjJbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class JkWjJbConvertImpl implements JkWjJbConvert {

    @Override
    public JkWjJbDTO convert(JkWjJbDO entity) {
        if ( entity == null ) {
            return null;
        }

        JkWjJbDTO jkWjJbDTO = new JkWjJbDTO();

        jkWjJbDTO.setId( entity.getId() );
        jkWjJbDTO.setWjbh( entity.getWjbh() );
        jkWjJbDTO.setWjywmc( entity.getWjywmc() );
        jkWjJbDTO.setWjywbh( entity.getWjywbh() );
        jkWjJbDTO.setWjmc( entity.getWjmc() );
        jkWjJbDTO.setWjdx( entity.getWjdx() );
        jkWjJbDTO.setWjlx( entity.getWjlx() );
        jkWjJbDTO.setWjsjdz( entity.getWjsjdz() );
        jkWjJbDTO.setHash( entity.getHash() );
        jkWjJbDTO.setFjsx( entity.getFjsx() );
        jkWjJbDTO.setLng( entity.getLng() );
        jkWjJbDTO.setLat( entity.getLat() );
        jkWjJbDTO.setAlt( entity.getAlt() );
        jkWjJbDTO.setCjsj( entity.getCjsj() );
        jkWjJbDTO.setCjrid( entity.getCjrid() );
        jkWjJbDTO.setCjr( entity.getCjr() );
        jkWjJbDTO.setCjrip( entity.getCjrip() );
        jkWjJbDTO.setCclx( entity.getCclx() );
        jkWjJbDTO.setQtsx( entity.getQtsx() );
        jkWjJbDTO.setKzsx1( entity.getKzsx1() );
        jkWjJbDTO.setKzsx2( entity.getKzsx2() );
        jkWjJbDTO.setKzsx3( entity.getKzsx3() );
        jkWjJbDTO.setKzsx4( entity.getKzsx4() );
        jkWjJbDTO.setKzsx5( entity.getKzsx5() );

        return jkWjJbDTO;
    }

    @Override
    public JkWjJbDO convertToDO(JkWjJbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        JkWjJbDO jkWjJbDO = new JkWjJbDO();

        jkWjJbDO.setId( dto.getId() );
        jkWjJbDO.setWjbh( dto.getWjbh() );
        jkWjJbDO.setWjywmc( dto.getWjywmc() );
        jkWjJbDO.setWjywbh( dto.getWjywbh() );
        jkWjJbDO.setWjmc( dto.getWjmc() );
        jkWjJbDO.setWjdx( dto.getWjdx() );
        jkWjJbDO.setWjlx( dto.getWjlx() );
        jkWjJbDO.setWjsjdz( dto.getWjsjdz() );
        jkWjJbDO.setHash( dto.getHash() );
        jkWjJbDO.setFjsx( dto.getFjsx() );
        jkWjJbDO.setLng( dto.getLng() );
        jkWjJbDO.setLat( dto.getLat() );
        jkWjJbDO.setAlt( dto.getAlt() );
        jkWjJbDO.setCjsj( dto.getCjsj() );
        jkWjJbDO.setCjrid( dto.getCjrid() );
        jkWjJbDO.setCjr( dto.getCjr() );
        jkWjJbDO.setCjrip( dto.getCjrip() );
        jkWjJbDO.setCclx( dto.getCclx() );
        jkWjJbDO.setQtsx( dto.getQtsx() );
        jkWjJbDO.setKzsx1( dto.getKzsx1() );
        jkWjJbDO.setKzsx2( dto.getKzsx2() );
        jkWjJbDO.setKzsx3( dto.getKzsx3() );
        jkWjJbDO.setKzsx4( dto.getKzsx4() );
        jkWjJbDO.setKzsx5( dto.getKzsx5() );

        return jkWjJbDO;
    }
}
