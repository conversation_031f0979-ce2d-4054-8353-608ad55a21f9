package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.WwTfbdjgxxbDTO;
import com.zjjcnt.project.ck.base.entity.WwTfbdjgxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class WwTfbdjgxxbConvertImpl implements WwTfbdjgxxbConvert {

    @Override
    public WwTfbdjgxxbDTO convert(WwTfbdjgxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        WwTfbdjgxxbDTO wwTfbdjgxxbDTO = new WwTfbdjgxxbDTO();

        wwTfbdjgxxbDTO.setId( entity.getId() );
        wwTfbdjgxxbDTO.setBdid( entity.getBdid() );
        wwTfbdjgxxbDTO.setPptj( entity.getPptj() );
        wwTfbdjgxxbDTO.setBdyj( entity.getBdyj() );
        wwTfbdjgxxbDTO.setCzsj( entity.getCzsj() );
        wwTfbdjgxxbDTO.setCzrid( entity.getCzrid() );
        wwTfbdjgxxbDTO.setCzrip( entity.getCzrip() );
        wwTfbdjgxxbDTO.setCzrdw( entity.getCzrdw() );
        wwTfbdjgxxbDTO.setCzrxm( entity.getCzrxm() );
        wwTfbdjgxxbDTO.setCzrdlm( entity.getCzrdlm() );
        wwTfbdjgxxbDTO.setBdywbh( entity.getBdywbh() );
        wwTfbdjgxxbDTO.setShrid( entity.getShrid() );
        wwTfbdjgxxbDTO.setShsj( entity.getShsj() );
        wwTfbdjgxxbDTO.setCljg( entity.getCljg() );
        wwTfbdjgxxbDTO.setClsm( entity.getClsm() );
        wwTfbdjgxxbDTO.setClrid( entity.getClrid() );
        wwTfbdjgxxbDTO.setClsj( entity.getClsj() );
        wwTfbdjgxxbDTO.setClrip( entity.getClrip() );
        wwTfbdjgxxbDTO.setSsxq( entity.getSsxq() );
        wwTfbdjgxxbDTO.setJlx( entity.getJlx() );
        wwTfbdjgxxbDTO.setMlph( entity.getMlph() );
        wwTfbdjgxxbDTO.setMlxz( entity.getMlxz() );
        wwTfbdjgxxbDTO.setPcs( entity.getPcs() );
        wwTfbdjgxxbDTO.setZrq( entity.getZrq() );
        wwTfbdjgxxbDTO.setXzjd( entity.getXzjd() );
        wwTfbdjgxxbDTO.setJcwh( entity.getJcwh() );
        wwTfbdjgxxbDTO.setRynbid( entity.getRynbid() );
        wwTfbdjgxxbDTO.setXm( entity.getXm() );
        wwTfbdjgxxbDTO.setGmsfhm( entity.getGmsfhm() );
        wwTfbdjgxxbDTO.setJlbz( entity.getJlbz() );

        return wwTfbdjgxxbDTO;
    }

    @Override
    public WwTfbdjgxxbDO convertToDO(WwTfbdjgxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        WwTfbdjgxxbDO wwTfbdjgxxbDO = new WwTfbdjgxxbDO();

        wwTfbdjgxxbDO.setId( dto.getId() );
        wwTfbdjgxxbDO.setBdid( dto.getBdid() );
        wwTfbdjgxxbDO.setPptj( dto.getPptj() );
        wwTfbdjgxxbDO.setBdyj( dto.getBdyj() );
        wwTfbdjgxxbDO.setCzsj( dto.getCzsj() );
        wwTfbdjgxxbDO.setCzrid( dto.getCzrid() );
        wwTfbdjgxxbDO.setCzrip( dto.getCzrip() );
        wwTfbdjgxxbDO.setCzrdw( dto.getCzrdw() );
        wwTfbdjgxxbDO.setCzrxm( dto.getCzrxm() );
        wwTfbdjgxxbDO.setCzrdlm( dto.getCzrdlm() );
        wwTfbdjgxxbDO.setBdywbh( dto.getBdywbh() );
        wwTfbdjgxxbDO.setShrid( dto.getShrid() );
        wwTfbdjgxxbDO.setShsj( dto.getShsj() );
        wwTfbdjgxxbDO.setCljg( dto.getCljg() );
        wwTfbdjgxxbDO.setClsm( dto.getClsm() );
        wwTfbdjgxxbDO.setClrid( dto.getClrid() );
        wwTfbdjgxxbDO.setClsj( dto.getClsj() );
        wwTfbdjgxxbDO.setClrip( dto.getClrip() );
        wwTfbdjgxxbDO.setSsxq( dto.getSsxq() );
        wwTfbdjgxxbDO.setJlx( dto.getJlx() );
        wwTfbdjgxxbDO.setMlph( dto.getMlph() );
        wwTfbdjgxxbDO.setMlxz( dto.getMlxz() );
        wwTfbdjgxxbDO.setPcs( dto.getPcs() );
        wwTfbdjgxxbDO.setZrq( dto.getZrq() );
        wwTfbdjgxxbDO.setXzjd( dto.getXzjd() );
        wwTfbdjgxxbDO.setJcwh( dto.getJcwh() );
        wwTfbdjgxxbDO.setRynbid( dto.getRynbid() );
        wwTfbdjgxxbDO.setXm( dto.getXm() );
        wwTfbdjgxxbDO.setGmsfhm( dto.getGmsfhm() );
        wwTfbdjgxxbDO.setJlbz( dto.getJlbz() );

        return wwTfbdjgxxbDO;
    }

    @Override
    public WwTfbdjgxxbDTO convertToDTO(HjxxCzrkjbxxbDTO czrkjbxxbDTO) {
        if ( czrkjbxxbDTO == null ) {
            return null;
        }

        WwTfbdjgxxbDTO wwTfbdjgxxbDTO = new WwTfbdjgxxbDTO();

        wwTfbdjgxxbDTO.setId( czrkjbxxbDTO.getId() );
        wwTfbdjgxxbDTO.setSsxq( czrkjbxxbDTO.getSsxq() );
        wwTfbdjgxxbDTO.setJlx( czrkjbxxbDTO.getJlx() );
        wwTfbdjgxxbDTO.setMlph( czrkjbxxbDTO.getMlph() );
        wwTfbdjgxxbDTO.setMlxz( czrkjbxxbDTO.getMlxz() );
        wwTfbdjgxxbDTO.setPcs( czrkjbxxbDTO.getPcs() );
        wwTfbdjgxxbDTO.setZrq( czrkjbxxbDTO.getZrq() );
        wwTfbdjgxxbDTO.setXzjd( czrkjbxxbDTO.getXzjd() );
        wwTfbdjgxxbDTO.setJcwh( czrkjbxxbDTO.getJcwh() );
        wwTfbdjgxxbDTO.setRynbid( czrkjbxxbDTO.getRynbid() );
        wwTfbdjgxxbDTO.setXm( czrkjbxxbDTO.getXm() );
        wwTfbdjgxxbDTO.setGmsfhm( czrkjbxxbDTO.getGmsfhm() );
        wwTfbdjgxxbDTO.setJlbz( czrkjbxxbDTO.getJlbz() );

        return wwTfbdjgxxbDTO;
    }
}
