package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.CrjScgjPcbDTO;
import com.zjjcnt.project.ck.base.dto.req.CrjScgjPcbCreateReq;
import com.zjjcnt.project.ck.base.dto.req.CrjScgjPcbPageReq;
import com.zjjcnt.project.ck.base.dto.req.CrjScgjPcbUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.CrjScgjPcbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.CrjScgjPcbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.CrjScgjPcbViewResp;
import com.zjjcnt.project.ck.base.entity.CrjScgjPcbDO;
import com.zjjcnt.project.ck.base.exp.CrjScgjPcbExp;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class CrjScgjPcbConvertImpl implements CrjScgjPcbConvert {

    @Override
    public CrjScgjPcbDTO convert(CrjScgjPcbDO entity) {
        if ( entity == null ) {
            return null;
        }

        CrjScgjPcbDTO crjScgjPcbDTO = new CrjScgjPcbDTO();

        crjScgjPcbDTO.setId( entity.getId() );
        crjScgjPcbDTO.setScgjpcid( entity.getScgjpcid() );
        crjScgjPcbDTO.setGj( entity.getGj() );
        crjScgjPcbDTO.setCzsj( entity.getCzsj() );
        crjScgjPcbDTO.setRs( entity.getRs() );
        crjScgjPcbDTO.setPczt( entity.getPczt() );
        crjScgjPcbDTO.setRksj( entity.getRksj() );

        return crjScgjPcbDTO;
    }

    @Override
    public CrjScgjPcbDO convertToDO(CrjScgjPcbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjPcbDO crjScgjPcbDO = new CrjScgjPcbDO();

        crjScgjPcbDO.setId( dto.getId() );
        crjScgjPcbDO.setScgjpcid( dto.getScgjpcid() );
        crjScgjPcbDO.setGj( dto.getGj() );
        crjScgjPcbDO.setCzsj( dto.getCzsj() );
        crjScgjPcbDO.setRs( dto.getRs() );
        crjScgjPcbDO.setPczt( dto.getPczt() );
        crjScgjPcbDO.setRksj( dto.getRksj() );

        return crjScgjPcbDO;
    }

    @Override
    public CrjScgjPcbDTO convertToDTO(CrjScgjPcbPageReq req) {
        if ( req == null ) {
            return null;
        }

        CrjScgjPcbDTO crjScgjPcbDTO = new CrjScgjPcbDTO();

        crjScgjPcbDTO.setGj( req.getGj() );
        crjScgjPcbDTO.setPczt( req.getPczt() );
        crjScgjPcbDTO.setCzsjStart( req.getCzsjStart() );
        crjScgjPcbDTO.setCzsjEnd( req.getCzsjEnd() );
        crjScgjPcbDTO.setRksjStart( req.getRksjStart() );
        crjScgjPcbDTO.setRksjEnd( req.getRksjEnd() );

        return crjScgjPcbDTO;
    }

    @Override
    public CrjScgjPcbDTO convertToDTO(CrjScgjPcbCreateReq req) {
        if ( req == null ) {
            return null;
        }

        CrjScgjPcbDTO crjScgjPcbDTO = new CrjScgjPcbDTO();

        crjScgjPcbDTO.setGj( req.getGj() );
        crjScgjPcbDTO.setCzsj( req.getCzsj() );

        return crjScgjPcbDTO;
    }

    @Override
    public CrjScgjPcbDTO convertToDTO(CrjScgjPcbUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        CrjScgjPcbDTO crjScgjPcbDTO = new CrjScgjPcbDTO();

        crjScgjPcbDTO.setScgjpcid( req.getScgjpcid() );
        crjScgjPcbDTO.setGj( req.getGj() );
        crjScgjPcbDTO.setCzsj( req.getCzsj() );

        return crjScgjPcbDTO;
    }

    @Override
    public CrjScgjPcbPageResp convertToPageResp(CrjScgjPcbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjPcbPageResp crjScgjPcbPageResp = new CrjScgjPcbPageResp();

        crjScgjPcbPageResp.setScgjpcid( dto.getScgjpcid() );
        crjScgjPcbPageResp.setGj( dto.getGj() );
        crjScgjPcbPageResp.setCzsj( dto.getCzsj() );
        crjScgjPcbPageResp.setRs( dto.getRs() );
        crjScgjPcbPageResp.setPczt( dto.getPczt() );
        crjScgjPcbPageResp.setRksj( dto.getRksj() );

        return crjScgjPcbPageResp;
    }

    @Override
    public CrjScgjPcbViewResp convertToViewResp(CrjScgjPcbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjPcbViewResp crjScgjPcbViewResp = new CrjScgjPcbViewResp();

        crjScgjPcbViewResp.setScgjpcid( dto.getScgjpcid() );
        crjScgjPcbViewResp.setGj( dto.getGj() );
        crjScgjPcbViewResp.setCzsj( dto.getCzsj() );
        crjScgjPcbViewResp.setRs( dto.getRs() );
        crjScgjPcbViewResp.setPczt( dto.getPczt() );
        crjScgjPcbViewResp.setRksj( dto.getRksj() );

        return crjScgjPcbViewResp;
    }

    @Override
    public CrjScgjPcbCreateResp convertToCreateResp(CrjScgjPcbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjPcbCreateResp crjScgjPcbCreateResp = new CrjScgjPcbCreateResp();

        crjScgjPcbCreateResp.setScgjpcid( dto.getScgjpcid() );
        crjScgjPcbCreateResp.setGj( dto.getGj() );
        crjScgjPcbCreateResp.setCzsj( dto.getCzsj() );
        crjScgjPcbCreateResp.setRs( dto.getRs() );
        crjScgjPcbCreateResp.setPczt( dto.getPczt() );
        crjScgjPcbCreateResp.setRksj( dto.getRksj() );

        return crjScgjPcbCreateResp;
    }

    @Override
    public CrjScgjPcbExp convertToExp(CrjScgjPcbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        CrjScgjPcbExp crjScgjPcbExp = new CrjScgjPcbExp();

        crjScgjPcbExp.setScgjpcid( dto.getScgjpcid() );
        crjScgjPcbExp.setGj( dto.getGj() );
        crjScgjPcbExp.setCzsj( dto.getCzsj() );
        crjScgjPcbExp.setRs( dto.getRs() );
        crjScgjPcbExp.setPczt( dto.getPczt() );
        crjScgjPcbExp.setRksj( dto.getRksj() );

        return crjScgjPcbExp;
    }
}
