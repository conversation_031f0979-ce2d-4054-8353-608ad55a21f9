package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtZwcjbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.base.entity.ZjtZwcjbDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:56+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtZwcjbConvertImpl implements ZjtZwcjbConvert {

    @Override
    public ZjtZwcjbDTO convert(ZjtZwcjbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtZwcjbDTO zjtZwcjbDTO = new ZjtZwcjbDTO();

        zjtZwcjbDTO.setId( entity.getId() );
        zjtZwcjbDTO.setZwtxid( entity.getZwtxid() );
        zjtZwcjbDTO.setRyid( entity.getRyid() );
        zjtZwcjbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtZwcjbDTO.setXm( entity.getXm() );
        zjtZwcjbDTO.setZwcjjgdm( entity.getZwcjjgdm() );
        zjtZwcjbDTO.setSzyczkdm( entity.getSzyczkdm() );
        zjtZwcjbDTO.setZwyzcjg( entity.getZwyzcjg() );
        zjtZwcjbDTO.setZwyzw( entity.getZwyzw() );
        zjtZwcjbDTO.setZwytxwjbh( entity.getZwytxwjbh() );
        zjtZwcjbDTO.setZwytxzlz( entity.getZwytxzlz() );
        byte[] zwyzwtzsj = entity.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjbDTO.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjbDTO.setZwezcjg( entity.getZwezcjg() );
        zjtZwcjbDTO.setZwezw( entity.getZwezw() );
        zjtZwcjbDTO.setZwetxwjbh( entity.getZwetxwjbh() );
        zjtZwcjbDTO.setZwetxzlz( entity.getZwetxzlz() );
        byte[] zwezwtzsj = entity.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjbDTO.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjbDTO.setZwqdxtzch( entity.getZwqdxtzch() );
        zjtZwcjbDTO.setZwcjqid( entity.getZwcjqid() );
        zjtZwcjbDTO.setZwsfbbh( entity.getZwsfbbh() );
        zjtZwcjbDTO.setZwsfkfzdm( entity.getZwsfkfzdm() );
        zjtZwcjbDTO.setCzrid( entity.getCzrid() );
        zjtZwcjbDTO.setBcsj( entity.getBcsj() );
        zjtZwcjbDTO.setCzyxm( entity.getCzyxm() );
        zjtZwcjbDTO.setCzydwdm( entity.getCzydwdm() );
        zjtZwcjbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtZwcjbDTO.setSbsj( entity.getSbsj() );
        zjtZwcjbDTO.setSlh( entity.getSlh() );
        zjtZwcjbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtZwcjbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtZwcjbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtZwcjbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtZwcjbDTO.setCjsj( entity.getCjsj() );

        return zjtZwcjbDTO;
    }

    @Override
    public ZjtZwcjbDO convertToDO(ZjtZwcjbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjbDO zjtZwcjbDO = new ZjtZwcjbDO();

        zjtZwcjbDO.setId( dto.getId() );
        zjtZwcjbDO.setZwtxid( dto.getZwtxid() );
        zjtZwcjbDO.setRyid( dto.getRyid() );
        zjtZwcjbDO.setGmsfhm( dto.getGmsfhm() );
        zjtZwcjbDO.setXm( dto.getXm() );
        zjtZwcjbDO.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtZwcjbDO.setSzyczkdm( dto.getSzyczkdm() );
        zjtZwcjbDO.setZwyzcjg( dto.getZwyzcjg() );
        zjtZwcjbDO.setZwyzw( dto.getZwyzw() );
        zjtZwcjbDO.setZwytxwjbh( dto.getZwytxwjbh() );
        zjtZwcjbDO.setZwytxzlz( dto.getZwytxzlz() );
        byte[] zwyzwtzsj = dto.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjbDO.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjbDO.setZwezcjg( dto.getZwezcjg() );
        zjtZwcjbDO.setZwezw( dto.getZwezw() );
        zjtZwcjbDO.setZwetxwjbh( dto.getZwetxwjbh() );
        zjtZwcjbDO.setZwetxzlz( dto.getZwetxzlz() );
        byte[] zwezwtzsj = dto.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjbDO.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjbDO.setZwqdxtzch( dto.getZwqdxtzch() );
        zjtZwcjbDO.setZwcjqid( dto.getZwcjqid() );
        zjtZwcjbDO.setZwsfbbh( dto.getZwsfbbh() );
        zjtZwcjbDO.setZwsfkfzdm( dto.getZwsfkfzdm() );
        zjtZwcjbDO.setCzrid( dto.getCzrid() );
        zjtZwcjbDO.setBcsj( dto.getBcsj() );
        zjtZwcjbDO.setCzyxm( dto.getCzyxm() );
        zjtZwcjbDO.setCzydwdm( dto.getCzydwdm() );
        zjtZwcjbDO.setCzydwmc( dto.getCzydwmc() );
        zjtZwcjbDO.setSbsj( dto.getSbsj() );
        zjtZwcjbDO.setSlh( dto.getSlh() );
        zjtZwcjbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtZwcjbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtZwcjbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtZwcjbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtZwcjbDO.setCjsj( dto.getCjsj() );

        return zjtZwcjbDO;
    }

    @Override
    public ZjtZwcjbDTO convertToDTO(ZjtZwcjlsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjbDTO zjtZwcjbDTO = new ZjtZwcjbDTO();

        zjtZwcjbDTO.setId( dto.getId() );
        zjtZwcjbDTO.setRyid( dto.getRyid() );
        zjtZwcjbDTO.setGmsfhm( dto.getGmsfhm() );
        zjtZwcjbDTO.setXm( dto.getXm() );
        zjtZwcjbDTO.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtZwcjbDTO.setSzyczkdm( dto.getSzyczkdm() );
        zjtZwcjbDTO.setZwyzcjg( dto.getZwyzcjg() );
        zjtZwcjbDTO.setZwyzw( dto.getZwyzw() );
        zjtZwcjbDTO.setZwytxzlz( dto.getZwytxzlz() );
        byte[] zwyzwtzsj = dto.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjbDTO.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjbDTO.setZwezcjg( dto.getZwezcjg() );
        zjtZwcjbDTO.setZwezw( dto.getZwezw() );
        zjtZwcjbDTO.setZwetxzlz( dto.getZwetxzlz() );
        byte[] zwezwtzsj = dto.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjbDTO.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjbDTO.setZwqdxtzch( dto.getZwqdxtzch() );
        zjtZwcjbDTO.setZwcjqid( dto.getZwcjqid() );
        zjtZwcjbDTO.setZwsfbbh( dto.getZwsfbbh() );
        zjtZwcjbDTO.setZwsfkfzdm( dto.getZwsfkfzdm() );
        zjtZwcjbDTO.setCzrid( dto.getCzrid() );
        zjtZwcjbDTO.setBcsj( dto.getBcsj() );
        zjtZwcjbDTO.setCzyxm( dto.getCzyxm() );
        zjtZwcjbDTO.setCzydwdm( dto.getCzydwdm() );
        zjtZwcjbDTO.setCzydwmc( dto.getCzydwmc() );
        zjtZwcjbDTO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtZwcjbDTO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtZwcjbDTO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtZwcjbDTO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        byte[] zwytxsj = dto.getZwytxsj();
        if ( zwytxsj != null ) {
            zjtZwcjbDTO.setZwytxsj( Arrays.copyOf( zwytxsj, zwytxsj.length ) );
        }
        byte[] zwetxsj = dto.getZwetxsj();
        if ( zwetxsj != null ) {
            zjtZwcjbDTO.setZwetxsj( Arrays.copyOf( zwetxsj, zwetxsj.length ) );
        }
        zjtZwcjbDTO.setBcsjStart( dto.getBcsjStart() );
        zjtZwcjbDTO.setBcsjEnd( dto.getBcsjEnd() );

        return zjtZwcjbDTO;
    }
}
