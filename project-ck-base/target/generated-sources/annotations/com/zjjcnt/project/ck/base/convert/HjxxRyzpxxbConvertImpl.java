package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxRyzpxxbDTO;
import com.zjjcnt.project.ck.base.dto.resp.HjxxRyzpxxbViewResp;
import com.zjjcnt.project.ck.base.entity.HjxxRyzpxxbDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:54+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxRyzpxxbConvertImpl implements HjxxRyzpxxbConvert {

    @Override
    public HjxxRyzpxxbDTO convert(HjxxRyzpxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxRyzpxxbDTO hjxxRyzpxxbDTO = new HjxxRyzpxxbDTO();

        hjxxRyzpxxbDTO.setId( entity.getId() );
        hjxxRyzpxxbDTO.setZpid( entity.getZpid() );
        hjxxRyzpxxbDTO.setRyid( entity.getRyid() );
        hjxxRyzpxxbDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxRyzpxxbDTO.setXm( entity.getXm() );
        byte[] zp = entity.getZp();
        if ( zp != null ) {
            hjxxRyzpxxbDTO.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        hjxxRyzpxxbDTO.setLrrq( entity.getLrrq() );
        hjxxRyzpxxbDTO.setDocfrom( entity.getDocfrom() );
        hjxxRyzpxxbDTO.setZpdx( entity.getZpdx() );
        hjxxRyzpxxbDTO.setZphash( entity.getZphash() );
        hjxxRyzpxxbDTO.setZpsjdzlx( entity.getZpsjdzlx() );
        hjxxRyzpxxbDTO.setZpsjdz( entity.getZpsjdz() );
        hjxxRyzpxxbDTO.setYxbz( entity.getYxbz() );
        hjxxRyzpxxbDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        hjxxRyzpxxbDTO.setScsj( entity.getScsj() );
        hjxxRyzpxxbDTO.setScrid( entity.getScrid() );
        hjxxRyzpxxbDTO.setScrip( entity.getScrip() );
        hjxxRyzpxxbDTO.setScrdwdm( entity.getScrdwdm() );

        return hjxxRyzpxxbDTO;
    }

    @Override
    public HjxxRyzpxxbDO convertToDO(HjxxRyzpxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxRyzpxxbDO hjxxRyzpxxbDO = new HjxxRyzpxxbDO();

        hjxxRyzpxxbDO.setId( dto.getId() );
        hjxxRyzpxxbDO.setZpid( dto.getZpid() );
        hjxxRyzpxxbDO.setRyid( dto.getRyid() );
        hjxxRyzpxxbDO.setGmsfhm( dto.getGmsfhm() );
        hjxxRyzpxxbDO.setXm( dto.getXm() );
        byte[] zp = dto.getZp();
        if ( zp != null ) {
            hjxxRyzpxxbDO.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        hjxxRyzpxxbDO.setLrrq( dto.getLrrq() );
        hjxxRyzpxxbDO.setDocfrom( dto.getDocfrom() );
        hjxxRyzpxxbDO.setZpdx( dto.getZpdx() );
        hjxxRyzpxxbDO.setZphash( dto.getZphash() );
        hjxxRyzpxxbDO.setZpsjdzlx( dto.getZpsjdzlx() );
        hjxxRyzpxxbDO.setZpsjdz( dto.getZpsjdz() );
        hjxxRyzpxxbDO.setYxbz( dto.getYxbz() );
        hjxxRyzpxxbDO.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxRyzpxxbDO.setScsj( dto.getScsj() );
        hjxxRyzpxxbDO.setScrid( dto.getScrid() );
        hjxxRyzpxxbDO.setScrip( dto.getScrip() );
        hjxxRyzpxxbDO.setScrdwdm( dto.getScrdwdm() );

        return hjxxRyzpxxbDO;
    }

    @Override
    public HjxxRyzpxxbViewResp convertToViewResp(HjxxRyzpxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxRyzpxxbViewResp hjxxRyzpxxbViewResp = new HjxxRyzpxxbViewResp();

        hjxxRyzpxxbViewResp.setZpid( dto.getZpid() );
        hjxxRyzpxxbViewResp.setRyid( dto.getRyid() );
        hjxxRyzpxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        hjxxRyzpxxbViewResp.setXm( dto.getXm() );
        hjxxRyzpxxbViewResp.setLrrq( dto.getLrrq() );
        hjxxRyzpxxbViewResp.setDocfrom( dto.getDocfrom() );
        hjxxRyzpxxbViewResp.setZpdx( dto.getZpdx() );
        hjxxRyzpxxbViewResp.setZphash( dto.getZphash() );
        hjxxRyzpxxbViewResp.setZpsjdzlx( dto.getZpsjdzlx() );
        hjxxRyzpxxbViewResp.setZpsjdz( dto.getZpsjdz() );
        hjxxRyzpxxbViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxRyzpxxbViewResp.setScsj( dto.getScsj() );
        hjxxRyzpxxbViewResp.setScrid( dto.getScrid() );
        hjxxRyzpxxbViewResp.setScrip( dto.getScrip() );
        hjxxRyzpxxbViewResp.setScrdwdm( dto.getScrdwdm() );

        hjxxRyzpxxbViewResp.setZp( dto.getZp() != null ? java.util.Base64.getEncoder().encodeToString(dto.getZp()) : "" );

        return hjxxRyzpxxbViewResp;
    }
}
