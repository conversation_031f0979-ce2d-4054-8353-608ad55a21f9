package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.JkGaggsjLwcxqqrzDTO;
import com.zjjcnt.project.ck.base.entity.JkGaggsjLwcxqqrzDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class JkGaggsjLwcxqqrzConvertImpl implements JkGaggsjLwcxqqrzConvert {

    @Override
    public JkGaggsjLwcxqqrzDTO convert(JkGaggsjLwcxqqrzDO entity) {
        if ( entity == null ) {
            return null;
        }

        JkGaggsjLwcxqqrzDTO jkGaggsjLwcxqqrzDTO = new JkGaggsjLwcxqqrzDTO();

        jkGaggsjLwcxqqrzDTO.setId( entity.getId() );
        jkGaggsjLwcxqqrzDTO.setNbbh( entity.getNbbh() );
        jkGaggsjLwcxqqrzDTO.setAppId( entity.getAppId() );
        jkGaggsjLwcxqqrzDTO.setExchangeServiceId( entity.getExchangeServiceId() );
        jkGaggsjLwcxqqrzDTO.setPowerMatters( entity.getPowerMatters() );
        jkGaggsjLwcxqqrzDTO.setSubPowerMatters( entity.getSubPowerMatters() );
        jkGaggsjLwcxqqrzDTO.setRequestUserName( entity.getRequestUserName() );
        jkGaggsjLwcxqqrzDTO.setRequestUserZjhm( entity.getRequestUserZjhm() );
        jkGaggsjLwcxqqrzDTO.setRequestDeptCode( entity.getRequestDeptCode() );
        jkGaggsjLwcxqqrzDTO.setRegId( entity.getRegId() );
        jkGaggsjLwcxqqrzDTO.setTerminalId( entity.getTerminalId() );
        jkGaggsjLwcxqqrzDTO.setUserName( entity.getUserName() );
        jkGaggsjLwcxqqrzDTO.setUserId( entity.getUserId() );
        jkGaggsjLwcxqqrzDTO.setOrganization( entity.getOrganization() );
        jkGaggsjLwcxqqrzDTO.setOrganizationId( entity.getOrganizationId() );
        jkGaggsjLwcxqqrzDTO.setRequestSendData( entity.getRequestSendData() );
        jkGaggsjLwcxqqrzDTO.setSendDataGmsfhm( entity.getSendDataGmsfhm() );
        jkGaggsjLwcxqqrzDTO.setSendDataXm( entity.getSendDataXm() );
        jkGaggsjLwcxqqrzDTO.setRequestResultWjjb( entity.getRequestResultWjjb() );
        jkGaggsjLwcxqqrzDTO.setRequestSendTime( entity.getRequestSendTime() );
        jkGaggsjLwcxqqrzDTO.setRequestEndTime( entity.getRequestEndTime() );
        jkGaggsjLwcxqqrzDTO.setResultCode( entity.getResultCode() );
        jkGaggsjLwcxqqrzDTO.setResultMsg( entity.getResultMsg() );
        jkGaggsjLwcxqqrzDTO.setResultDataCount( entity.getResultDataCount() );
        jkGaggsjLwcxqqrzDTO.setResultCode3rd( entity.getResultCode3rd() );
        jkGaggsjLwcxqqrzDTO.setResultMsg3rd( entity.getResultMsg3rd() );
        jkGaggsjLwcxqqrzDTO.setResultCodeZw( entity.getResultCodeZw() );
        jkGaggsjLwcxqqrzDTO.setResultMsgZw( entity.getResultMsgZw() );
        jkGaggsjLwcxqqrzDTO.setInvokeStatus( entity.getInvokeStatus() );
        jkGaggsjLwcxqqrzDTO.setInvokeError( entity.getInvokeError() );
        jkGaggsjLwcxqqrzDTO.setInvokeSys( entity.getInvokeSys() );
        jkGaggsjLwcxqqrzDTO.setInvokeZjhm( entity.getInvokeZjhm() );
        jkGaggsjLwcxqqrzDTO.setInvokeId( entity.getInvokeId() );
        jkGaggsjLwcxqqrzDTO.setInvokeMc( entity.getInvokeMc() );
        jkGaggsjLwcxqqrzDTO.setInvokeSzbmMc( entity.getInvokeSzbmMc() );
        jkGaggsjLwcxqqrzDTO.setInvokeSzbm( entity.getInvokeSzbm() );
        jkGaggsjLwcxqqrzDTO.setInvokeHost( entity.getInvokeHost() );
        jkGaggsjLwcxqqrzDTO.setCjsj( entity.getCjsj() );
        jkGaggsjLwcxqqrzDTO.setCjr( entity.getCjr() );
        jkGaggsjLwcxqqrzDTO.setCjrip( entity.getCjrip() );
        jkGaggsjLwcxqqrzDTO.setCjrid( entity.getCjrid() );
        jkGaggsjLwcxqqrzDTO.setConfigId( entity.getConfigId() );
        jkGaggsjLwcxqqrzDTO.setInvokeServiceName( entity.getInvokeServiceName() );
        jkGaggsjLwcxqqrzDTO.setExchangeServiceName( entity.getExchangeServiceName() );

        return jkGaggsjLwcxqqrzDTO;
    }

    @Override
    public JkGaggsjLwcxqqrzDO convertToDO(JkGaggsjLwcxqqrzDTO dto) {
        if ( dto == null ) {
            return null;
        }

        JkGaggsjLwcxqqrzDO jkGaggsjLwcxqqrzDO = new JkGaggsjLwcxqqrzDO();

        jkGaggsjLwcxqqrzDO.setId( dto.getId() );
        jkGaggsjLwcxqqrzDO.setNbbh( dto.getNbbh() );
        jkGaggsjLwcxqqrzDO.setAppId( dto.getAppId() );
        jkGaggsjLwcxqqrzDO.setExchangeServiceId( dto.getExchangeServiceId() );
        jkGaggsjLwcxqqrzDO.setPowerMatters( dto.getPowerMatters() );
        jkGaggsjLwcxqqrzDO.setSubPowerMatters( dto.getSubPowerMatters() );
        jkGaggsjLwcxqqrzDO.setRequestUserName( dto.getRequestUserName() );
        jkGaggsjLwcxqqrzDO.setRequestUserZjhm( dto.getRequestUserZjhm() );
        jkGaggsjLwcxqqrzDO.setRequestDeptCode( dto.getRequestDeptCode() );
        jkGaggsjLwcxqqrzDO.setRegId( dto.getRegId() );
        jkGaggsjLwcxqqrzDO.setTerminalId( dto.getTerminalId() );
        jkGaggsjLwcxqqrzDO.setUserName( dto.getUserName() );
        jkGaggsjLwcxqqrzDO.setUserId( dto.getUserId() );
        jkGaggsjLwcxqqrzDO.setOrganization( dto.getOrganization() );
        jkGaggsjLwcxqqrzDO.setOrganizationId( dto.getOrganizationId() );
        jkGaggsjLwcxqqrzDO.setRequestSendData( dto.getRequestSendData() );
        jkGaggsjLwcxqqrzDO.setSendDataGmsfhm( dto.getSendDataGmsfhm() );
        jkGaggsjLwcxqqrzDO.setSendDataXm( dto.getSendDataXm() );
        jkGaggsjLwcxqqrzDO.setRequestResultWjjb( dto.getRequestResultWjjb() );
        jkGaggsjLwcxqqrzDO.setRequestSendTime( dto.getRequestSendTime() );
        jkGaggsjLwcxqqrzDO.setRequestEndTime( dto.getRequestEndTime() );
        jkGaggsjLwcxqqrzDO.setResultCode( dto.getResultCode() );
        jkGaggsjLwcxqqrzDO.setResultMsg( dto.getResultMsg() );
        jkGaggsjLwcxqqrzDO.setResultDataCount( dto.getResultDataCount() );
        jkGaggsjLwcxqqrzDO.setResultCode3rd( dto.getResultCode3rd() );
        jkGaggsjLwcxqqrzDO.setResultMsg3rd( dto.getResultMsg3rd() );
        jkGaggsjLwcxqqrzDO.setResultCodeZw( dto.getResultCodeZw() );
        jkGaggsjLwcxqqrzDO.setResultMsgZw( dto.getResultMsgZw() );
        jkGaggsjLwcxqqrzDO.setInvokeStatus( dto.getInvokeStatus() );
        jkGaggsjLwcxqqrzDO.setInvokeError( dto.getInvokeError() );
        jkGaggsjLwcxqqrzDO.setInvokeSys( dto.getInvokeSys() );
        jkGaggsjLwcxqqrzDO.setInvokeZjhm( dto.getInvokeZjhm() );
        jkGaggsjLwcxqqrzDO.setInvokeId( dto.getInvokeId() );
        jkGaggsjLwcxqqrzDO.setInvokeMc( dto.getInvokeMc() );
        jkGaggsjLwcxqqrzDO.setInvokeSzbmMc( dto.getInvokeSzbmMc() );
        jkGaggsjLwcxqqrzDO.setInvokeSzbm( dto.getInvokeSzbm() );
        jkGaggsjLwcxqqrzDO.setInvokeHost( dto.getInvokeHost() );
        jkGaggsjLwcxqqrzDO.setCjsj( dto.getCjsj() );
        jkGaggsjLwcxqqrzDO.setCjr( dto.getCjr() );
        jkGaggsjLwcxqqrzDO.setCjrip( dto.getCjrip() );
        jkGaggsjLwcxqqrzDO.setCjrid( dto.getCjrid() );
        jkGaggsjLwcxqqrzDO.setConfigId( dto.getConfigId() );
        jkGaggsjLwcxqqrzDO.setInvokeServiceName( dto.getInvokeServiceName() );
        jkGaggsjLwcxqqrzDO.setExchangeServiceName( dto.getExchangeServiceName() );

        return jkGaggsjLwcxqqrzDO;
    }
}
