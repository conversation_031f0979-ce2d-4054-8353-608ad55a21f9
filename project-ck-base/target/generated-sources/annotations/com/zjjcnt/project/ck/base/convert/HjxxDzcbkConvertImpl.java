package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.HjxxDzcbkDTO;
import com.zjjcnt.project.ck.base.entity.HjxxDzcbkDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:58+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxDzcbkConvertImpl implements HjxxDzcbkConvert {

    @Override
    public HjxxDzcbkDTO convert(HjxxDzcbkDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxDzcbkDTO hjxxDzcbkDTO = new HjxxDzcbkDTO();

        hjxxDzcbkDTO.setId( entity.getId() );
        hjxxDzcbkDTO.setDzcbkid( entity.getDzcbkid() );
        hjxxDzcbkDTO.setSlclbh( entity.getSlclbh() );
        hjxxDzcbkDTO.setYwslh( entity.getYwslh() );
        hjxxDzcbkDTO.setLcywlx( entity.getLcywlx() );
        hjxxDzcbkDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxDzcbkDTO.setXm( entity.getXm() );
        hjxxDzcbkDTO.setCllxdm( entity.getCllxdm() );
        hjxxDzcbkDTO.setCllxmc( entity.getCllxmc() );
        hjxxDzcbkDTO.setClmc( entity.getClmc() );
        hjxxDzcbkDTO.setWjdx( entity.getWjdx() );
        hjxxDzcbkDTO.setWjlx( entity.getWjlx() );
        hjxxDzcbkDTO.setWjsjdz( entity.getWjsjdz() );
        hjxxDzcbkDTO.setWjhash( entity.getWjhash() );
        hjxxDzcbkDTO.setWjsjdzlx( entity.getWjsjdzlx() );
        byte[] wjdata = entity.getWjdata();
        if ( wjdata != null ) {
            hjxxDzcbkDTO.setWjdata( Arrays.copyOf( wjdata, wjdata.length ) );
        }
        hjxxDzcbkDTO.setClcjrid( entity.getClcjrid() );
        hjxxDzcbkDTO.setClcjr( entity.getClcjr() );
        hjxxDzcbkDTO.setClcjsj( entity.getClcjsj() );
        hjxxDzcbkDTO.setRyid( entity.getRyid() );
        hjxxDzcbkDTO.setRynbid( entity.getRynbid() );
        hjxxDzcbkDTO.setHhid( entity.getHhid() );
        hjxxDzcbkDTO.setHhnbid( entity.getHhnbid() );
        hjxxDzcbkDTO.setMlpid( entity.getMlpid() );
        hjxxDzcbkDTO.setMlpnbid( entity.getMlpnbid() );
        hjxxDzcbkDTO.setQysj( entity.getQysj() );
        hjxxDzcbkDTO.setJssj( entity.getJssj() );
        hjxxDzcbkDTO.setXxqysj( entity.getXxqysj() );
        hjxxDzcbkDTO.setJlbz( entity.getJlbz() );
        hjxxDzcbkDTO.setCxbz( entity.getCxbz() );
        hjxxDzcbkDTO.setRyzt( entity.getRyzt() );
        hjxxDzcbkDTO.setSsxq( entity.getSsxq() );
        hjxxDzcbkDTO.setXzjd( entity.getXzjd() );
        hjxxDzcbkDTO.setJlx( entity.getJlx() );
        hjxxDzcbkDTO.setMlph( entity.getMlph() );
        hjxxDzcbkDTO.setMlxz( entity.getMlxz() );
        hjxxDzcbkDTO.setPcs( entity.getPcs() );
        hjxxDzcbkDTO.setJcwh( entity.getJcwh() );
        hjxxDzcbkDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        hjxxDzcbkDTO.setSjgsdwmc( entity.getSjgsdwmc() );

        return hjxxDzcbkDTO;
    }

    @Override
    public HjxxDzcbkDO convertToDO(HjxxDzcbkDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxDzcbkDO hjxxDzcbkDO = new HjxxDzcbkDO();

        hjxxDzcbkDO.setId( dto.getId() );
        hjxxDzcbkDO.setDzcbkid( dto.getDzcbkid() );
        hjxxDzcbkDO.setSlclbh( dto.getSlclbh() );
        hjxxDzcbkDO.setYwslh( dto.getYwslh() );
        hjxxDzcbkDO.setLcywlx( dto.getLcywlx() );
        hjxxDzcbkDO.setGmsfhm( dto.getGmsfhm() );
        hjxxDzcbkDO.setXm( dto.getXm() );
        hjxxDzcbkDO.setCllxdm( dto.getCllxdm() );
        hjxxDzcbkDO.setCllxmc( dto.getCllxmc() );
        hjxxDzcbkDO.setClmc( dto.getClmc() );
        hjxxDzcbkDO.setWjdx( dto.getWjdx() );
        hjxxDzcbkDO.setWjlx( dto.getWjlx() );
        hjxxDzcbkDO.setWjsjdz( dto.getWjsjdz() );
        hjxxDzcbkDO.setWjhash( dto.getWjhash() );
        hjxxDzcbkDO.setWjsjdzlx( dto.getWjsjdzlx() );
        byte[] wjdata = dto.getWjdata();
        if ( wjdata != null ) {
            hjxxDzcbkDO.setWjdata( Arrays.copyOf( wjdata, wjdata.length ) );
        }
        hjxxDzcbkDO.setClcjrid( dto.getClcjrid() );
        hjxxDzcbkDO.setClcjr( dto.getClcjr() );
        hjxxDzcbkDO.setClcjsj( dto.getClcjsj() );
        hjxxDzcbkDO.setRyid( dto.getRyid() );
        hjxxDzcbkDO.setRynbid( dto.getRynbid() );
        hjxxDzcbkDO.setHhid( dto.getHhid() );
        hjxxDzcbkDO.setHhnbid( dto.getHhnbid() );
        hjxxDzcbkDO.setMlpid( dto.getMlpid() );
        hjxxDzcbkDO.setMlpnbid( dto.getMlpnbid() );
        hjxxDzcbkDO.setQysj( dto.getQysj() );
        hjxxDzcbkDO.setJssj( dto.getJssj() );
        hjxxDzcbkDO.setXxqysj( dto.getXxqysj() );
        hjxxDzcbkDO.setJlbz( dto.getJlbz() );
        hjxxDzcbkDO.setCxbz( dto.getCxbz() );
        hjxxDzcbkDO.setRyzt( dto.getRyzt() );
        hjxxDzcbkDO.setSsxq( dto.getSsxq() );
        hjxxDzcbkDO.setXzjd( dto.getXzjd() );
        hjxxDzcbkDO.setJlx( dto.getJlx() );
        hjxxDzcbkDO.setMlph( dto.getMlph() );
        hjxxDzcbkDO.setMlxz( dto.getMlxz() );
        hjxxDzcbkDO.setPcs( dto.getPcs() );
        hjxxDzcbkDO.setJcwh( dto.getJcwh() );
        hjxxDzcbkDO.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxDzcbkDO.setSjgsdwmc( dto.getSjgsdwmc() );

        return hjxxDzcbkDO;
    }

    @Override
    public void convertToDTO(HjxxCzrkjbxxbDTO czrkjbxxbDTO, HjxxDzcbkDTO dto) {
        if ( czrkjbxxbDTO == null ) {
            return;
        }

        dto.setGmsfhm( czrkjbxxbDTO.getGmsfhm() );
        dto.setXm( czrkjbxxbDTO.getXm() );
        dto.setRyid( czrkjbxxbDTO.getRyid() );
        dto.setRynbid( czrkjbxxbDTO.getRynbid() );
        dto.setHhid( czrkjbxxbDTO.getHhid() );
        dto.setHhnbid( czrkjbxxbDTO.getHhnbid() );
        dto.setMlpid( czrkjbxxbDTO.getMlpid() );
        dto.setMlpnbid( czrkjbxxbDTO.getMlpnbid() );
        dto.setQysj( czrkjbxxbDTO.getQysj() );
        dto.setJssj( czrkjbxxbDTO.getJssj() );
        dto.setXxqysj( czrkjbxxbDTO.getXxqysj() );
        dto.setJlbz( czrkjbxxbDTO.getJlbz() );
        dto.setCxbz( czrkjbxxbDTO.getCxbz() );
        dto.setRyzt( czrkjbxxbDTO.getRyzt() );
        dto.setSsxq( czrkjbxxbDTO.getSsxq() );
        dto.setXzjd( czrkjbxxbDTO.getXzjd() );
        dto.setJlx( czrkjbxxbDTO.getJlx() );
        dto.setMlph( czrkjbxxbDTO.getMlph() );
        dto.setMlxz( czrkjbxxbDTO.getMlxz() );
        dto.setPcs( czrkjbxxbDTO.getPcs() );
        dto.setJcwh( czrkjbxxbDTO.getJcwh() );
        dto.setSjgsdwdm( czrkjbxxbDTO.getSjgsdwdm() );
        dto.setSjgsdwmc( czrkjbxxbDTO.getSjgsdwmc() );
        dto.setQysjStart( czrkjbxxbDTO.getQysjStart() );
        dto.setQysjEnd( czrkjbxxbDTO.getQysjEnd() );
        dto.setJssjStart( czrkjbxxbDTO.getJssjStart() );
        dto.setJssjEnd( czrkjbxxbDTO.getJssjEnd() );
        dto.setXxqysjStart( czrkjbxxbDTO.getXxqysjStart() );
        dto.setXxqysjEnd( czrkjbxxbDTO.getXxqysjEnd() );
    }
}
