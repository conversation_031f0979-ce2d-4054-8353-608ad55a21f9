package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtZwcjlsbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtZwcjlsbCreateReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjlsbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjlsbViewResp;
import com.zjjcnt.project.ck.base.entity.ZjtZwcjlsbDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtZwcjlsbConvertImpl implements ZjtZwcjlsbConvert {

    @Override
    public ZjtZwcjlsbDTO convert(ZjtZwcjlsbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtZwcjlsbDTO zjtZwcjlsbDTO = new ZjtZwcjlsbDTO();

        zjtZwcjlsbDTO.setId( entity.getId() );
        zjtZwcjlsbDTO.setZwtxid( entity.getZwtxid() );
        zjtZwcjlsbDTO.setRyid( entity.getRyid() );
        zjtZwcjlsbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtZwcjlsbDTO.setXm( entity.getXm() );
        zjtZwcjlsbDTO.setZwcjjgdm( entity.getZwcjjgdm() );
        zjtZwcjlsbDTO.setSzyczkdm( entity.getSzyczkdm() );
        zjtZwcjlsbDTO.setZwyzcjg( entity.getZwyzcjg() );
        zjtZwcjlsbDTO.setZwyzw( entity.getZwyzw() );
        byte[] zwytxsj = entity.getZwytxsj();
        if ( zwytxsj != null ) {
            zjtZwcjlsbDTO.setZwytxsj( Arrays.copyOf( zwytxsj, zwytxsj.length ) );
        }
        zjtZwcjlsbDTO.setZwytxzlz( entity.getZwytxzlz() );
        byte[] zwyzwtzsj = entity.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjlsbDTO.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjlsbDTO.setZwezcjg( entity.getZwezcjg() );
        zjtZwcjlsbDTO.setZwezw( entity.getZwezw() );
        byte[] zwetxsj = entity.getZwetxsj();
        if ( zwetxsj != null ) {
            zjtZwcjlsbDTO.setZwetxsj( Arrays.copyOf( zwetxsj, zwetxsj.length ) );
        }
        zjtZwcjlsbDTO.setZwetxzlz( entity.getZwetxzlz() );
        byte[] zwezwtzsj = entity.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjlsbDTO.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjlsbDTO.setZwqdxtzch( entity.getZwqdxtzch() );
        zjtZwcjlsbDTO.setZwcjqid( entity.getZwcjqid() );
        zjtZwcjlsbDTO.setZwsfbbh( entity.getZwsfbbh() );
        zjtZwcjlsbDTO.setZwsfkfzdm( entity.getZwsfkfzdm() );
        zjtZwcjlsbDTO.setCzrid( entity.getCzrid() );
        zjtZwcjlsbDTO.setBcsj( entity.getBcsj() );
        zjtZwcjlsbDTO.setCzyxm( entity.getCzyxm() );
        zjtZwcjlsbDTO.setCzydwdm( entity.getCzydwdm() );
        zjtZwcjlsbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtZwcjlsbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtZwcjlsbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtZwcjlsbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtZwcjlsbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );

        return zjtZwcjlsbDTO;
    }

    @Override
    public ZjtZwcjlsbDO convertToDO(ZjtZwcjlsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjlsbDO zjtZwcjlsbDO = new ZjtZwcjlsbDO();

        zjtZwcjlsbDO.setId( dto.getId() );
        zjtZwcjlsbDO.setZwtxid( dto.getZwtxid() );
        zjtZwcjlsbDO.setRyid( dto.getRyid() );
        zjtZwcjlsbDO.setGmsfhm( dto.getGmsfhm() );
        zjtZwcjlsbDO.setXm( dto.getXm() );
        zjtZwcjlsbDO.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtZwcjlsbDO.setSzyczkdm( dto.getSzyczkdm() );
        zjtZwcjlsbDO.setZwyzcjg( dto.getZwyzcjg() );
        zjtZwcjlsbDO.setZwyzw( dto.getZwyzw() );
        byte[] zwytxsj = dto.getZwytxsj();
        if ( zwytxsj != null ) {
            zjtZwcjlsbDO.setZwytxsj( Arrays.copyOf( zwytxsj, zwytxsj.length ) );
        }
        zjtZwcjlsbDO.setZwytxzlz( dto.getZwytxzlz() );
        byte[] zwyzwtzsj = dto.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjlsbDO.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjlsbDO.setZwezcjg( dto.getZwezcjg() );
        zjtZwcjlsbDO.setZwezw( dto.getZwezw() );
        byte[] zwetxsj = dto.getZwetxsj();
        if ( zwetxsj != null ) {
            zjtZwcjlsbDO.setZwetxsj( Arrays.copyOf( zwetxsj, zwetxsj.length ) );
        }
        zjtZwcjlsbDO.setZwetxzlz( dto.getZwetxzlz() );
        byte[] zwezwtzsj = dto.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjlsbDO.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjlsbDO.setZwqdxtzch( dto.getZwqdxtzch() );
        zjtZwcjlsbDO.setZwcjqid( dto.getZwcjqid() );
        zjtZwcjlsbDO.setZwsfbbh( dto.getZwsfbbh() );
        zjtZwcjlsbDO.setZwsfkfzdm( dto.getZwsfkfzdm() );
        zjtZwcjlsbDO.setCzrid( dto.getCzrid() );
        zjtZwcjlsbDO.setBcsj( dto.getBcsj() );
        zjtZwcjlsbDO.setCzyxm( dto.getCzyxm() );
        zjtZwcjlsbDO.setCzydwdm( dto.getCzydwdm() );
        zjtZwcjlsbDO.setCzydwmc( dto.getCzydwmc() );
        zjtZwcjlsbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtZwcjlsbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtZwcjlsbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtZwcjlsbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );

        return zjtZwcjlsbDO;
    }

    @Override
    public ZjtZwcjlsbDTO convertToDTO(ZjtZwcjlsbCreateReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtZwcjlsbDTO zjtZwcjlsbDTO = new ZjtZwcjlsbDTO();

        zjtZwcjlsbDTO.setRyid( req.getRyid() );
        zjtZwcjlsbDTO.setGmsfhm( req.getGmsfhm() );
        zjtZwcjlsbDTO.setXm( req.getXm() );
        zjtZwcjlsbDTO.setZwcjjgdm( req.getZwcjjgdm() );
        zjtZwcjlsbDTO.setSzyczkdm( req.getSzyczkdm() );
        zjtZwcjlsbDTO.setZwyzcjg( req.getZwyzcjg() );
        zjtZwcjlsbDTO.setZwyzw( req.getZwyzw() );
        byte[] zwytxsj = req.getZwytxsj();
        if ( zwytxsj != null ) {
            zjtZwcjlsbDTO.setZwytxsj( Arrays.copyOf( zwytxsj, zwytxsj.length ) );
        }
        zjtZwcjlsbDTO.setZwytxzlz( req.getZwytxzlz() );
        byte[] zwyzwtzsj = req.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjlsbDTO.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjlsbDTO.setZwezcjg( req.getZwezcjg() );
        zjtZwcjlsbDTO.setZwezw( req.getZwezw() );
        byte[] zwetxsj = req.getZwetxsj();
        if ( zwetxsj != null ) {
            zjtZwcjlsbDTO.setZwetxsj( Arrays.copyOf( zwetxsj, zwetxsj.length ) );
        }
        zjtZwcjlsbDTO.setZwetxzlz( req.getZwetxzlz() );
        byte[] zwezwtzsj = req.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjlsbDTO.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjlsbDTO.setZwqdxtzch( req.getZwqdxtzch() );
        zjtZwcjlsbDTO.setZwcjqid( req.getZwcjqid() );
        zjtZwcjlsbDTO.setZwsfbbh( req.getZwsfbbh() );
        zjtZwcjlsbDTO.setZwsfkfzdm( req.getZwsfkfzdm() );
        zjtZwcjlsbDTO.setHjdsjgsdwdm( req.getHjdsjgsdwdm() );
        zjtZwcjlsbDTO.setHjdsjgsdwmc( req.getHjdsjgsdwmc() );
        zjtZwcjlsbDTO.setSldsjgsdwdm( req.getSldsjgsdwdm() );
        zjtZwcjlsbDTO.setSldsjgsdwmc( req.getSldsjgsdwmc() );

        return zjtZwcjlsbDTO;
    }

    @Override
    public ZjtZwcjlsbViewResp convertToViewResp(ZjtZwcjlsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjlsbViewResp zjtZwcjlsbViewResp = new ZjtZwcjlsbViewResp();

        zjtZwcjlsbViewResp.setZwtxid( dto.getZwtxid() );
        zjtZwcjlsbViewResp.setRyid( dto.getRyid() );
        zjtZwcjlsbViewResp.setGmsfhm( dto.getGmsfhm() );
        zjtZwcjlsbViewResp.setXm( dto.getXm() );
        zjtZwcjlsbViewResp.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtZwcjlsbViewResp.setSzyczkdm( dto.getSzyczkdm() );
        zjtZwcjlsbViewResp.setZwyzcjg( dto.getZwyzcjg() );
        zjtZwcjlsbViewResp.setZwyzw( dto.getZwyzw() );
        byte[] zwytxsj = dto.getZwytxsj();
        if ( zwytxsj != null ) {
            zjtZwcjlsbViewResp.setZwytxsj( Arrays.copyOf( zwytxsj, zwytxsj.length ) );
        }
        zjtZwcjlsbViewResp.setZwytxzlz( dto.getZwytxzlz() );
        byte[] zwyzwtzsj = dto.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjlsbViewResp.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjlsbViewResp.setZwezcjg( dto.getZwezcjg() );
        zjtZwcjlsbViewResp.setZwezw( dto.getZwezw() );
        byte[] zwetxsj = dto.getZwetxsj();
        if ( zwetxsj != null ) {
            zjtZwcjlsbViewResp.setZwetxsj( Arrays.copyOf( zwetxsj, zwetxsj.length ) );
        }
        zjtZwcjlsbViewResp.setZwetxzlz( dto.getZwetxzlz() );
        byte[] zwezwtzsj = dto.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjlsbViewResp.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjlsbViewResp.setZwqdxtzch( dto.getZwqdxtzch() );
        zjtZwcjlsbViewResp.setZwcjqid( dto.getZwcjqid() );
        zjtZwcjlsbViewResp.setZwsfbbh( dto.getZwsfbbh() );
        zjtZwcjlsbViewResp.setZwsfkfzdm( dto.getZwsfkfzdm() );
        zjtZwcjlsbViewResp.setCzrid( dto.getCzrid() );
        zjtZwcjlsbViewResp.setBcsj( dto.getBcsj() );
        zjtZwcjlsbViewResp.setCzyxm( dto.getCzyxm() );
        zjtZwcjlsbViewResp.setCzydwdm( dto.getCzydwdm() );
        zjtZwcjlsbViewResp.setCzydwmc( dto.getCzydwmc() );
        zjtZwcjlsbViewResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtZwcjlsbViewResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtZwcjlsbViewResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtZwcjlsbViewResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );

        return zjtZwcjlsbViewResp;
    }

    @Override
    public ZjtZwcjlsbCreateResp convertToCreateResp(ZjtZwcjlsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjlsbCreateResp zjtZwcjlsbCreateResp = new ZjtZwcjlsbCreateResp();

        zjtZwcjlsbCreateResp.setZwtxid( dto.getZwtxid() );
        zjtZwcjlsbCreateResp.setRyid( dto.getRyid() );
        zjtZwcjlsbCreateResp.setGmsfhm( dto.getGmsfhm() );
        zjtZwcjlsbCreateResp.setXm( dto.getXm() );
        zjtZwcjlsbCreateResp.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtZwcjlsbCreateResp.setSzyczkdm( dto.getSzyczkdm() );
        zjtZwcjlsbCreateResp.setZwyzcjg( dto.getZwyzcjg() );
        zjtZwcjlsbCreateResp.setZwyzw( dto.getZwyzw() );
        byte[] zwytxsj = dto.getZwytxsj();
        if ( zwytxsj != null ) {
            zjtZwcjlsbCreateResp.setZwytxsj( Arrays.copyOf( zwytxsj, zwytxsj.length ) );
        }
        zjtZwcjlsbCreateResp.setZwytxzlz( dto.getZwytxzlz() );
        byte[] zwyzwtzsj = dto.getZwyzwtzsj();
        if ( zwyzwtzsj != null ) {
            zjtZwcjlsbCreateResp.setZwyzwtzsj( Arrays.copyOf( zwyzwtzsj, zwyzwtzsj.length ) );
        }
        zjtZwcjlsbCreateResp.setZwezcjg( dto.getZwezcjg() );
        zjtZwcjlsbCreateResp.setZwezw( dto.getZwezw() );
        byte[] zwetxsj = dto.getZwetxsj();
        if ( zwetxsj != null ) {
            zjtZwcjlsbCreateResp.setZwetxsj( Arrays.copyOf( zwetxsj, zwetxsj.length ) );
        }
        zjtZwcjlsbCreateResp.setZwetxzlz( dto.getZwetxzlz() );
        byte[] zwezwtzsj = dto.getZwezwtzsj();
        if ( zwezwtzsj != null ) {
            zjtZwcjlsbCreateResp.setZwezwtzsj( Arrays.copyOf( zwezwtzsj, zwezwtzsj.length ) );
        }
        zjtZwcjlsbCreateResp.setZwqdxtzch( dto.getZwqdxtzch() );
        zjtZwcjlsbCreateResp.setZwcjqid( dto.getZwcjqid() );
        zjtZwcjlsbCreateResp.setZwsfbbh( dto.getZwsfbbh() );
        zjtZwcjlsbCreateResp.setZwsfkfzdm( dto.getZwsfkfzdm() );
        zjtZwcjlsbCreateResp.setCzrid( dto.getCzrid() );
        zjtZwcjlsbCreateResp.setBcsj( dto.getBcsj() );
        zjtZwcjlsbCreateResp.setCzyxm( dto.getCzyxm() );
        zjtZwcjlsbCreateResp.setCzydwdm( dto.getCzydwdm() );
        zjtZwcjlsbCreateResp.setCzydwmc( dto.getCzydwmc() );
        zjtZwcjlsbCreateResp.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtZwcjlsbCreateResp.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtZwcjlsbCreateResp.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtZwcjlsbCreateResp.setSldsjgsdwmc( dto.getSldsjgsdwmc() );

        return zjtZwcjlsbCreateResp;
    }
}
