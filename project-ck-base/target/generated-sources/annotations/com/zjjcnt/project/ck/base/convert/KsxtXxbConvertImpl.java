package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.KsxtXxbDTO;
import com.zjjcnt.project.ck.base.dto.req.KsxtXxbCreateReq;
import com.zjjcnt.project.ck.base.dto.req.KsxtXxbPageReq;
import com.zjjcnt.project.ck.base.dto.req.KsxtXxbUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.KsxtXxbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtXxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.KsxtXxbViewResp;
import com.zjjcnt.project.ck.base.entity.KsxtXxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:55+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class KsxtXxbConvertImpl implements KsxtXxbConvert {

    @Override
    public KsxtXxbDTO convert(KsxtXxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        KsxtXxbDTO ksxtXxbDTO = new KsxtXxbDTO();

        ksxtXxbDTO.setId( entity.getId() );
        ksxtXxbDTO.setXxbt( entity.getXxbt() );
        ksxtXxbDTO.setXxnr( entity.getXxnr() );
        ksxtXxbDTO.setXxzt( entity.getXxzt() );
        ksxtXxbDTO.setYxbz( entity.getYxbz() );
        ksxtXxbDTO.setCjsj( entity.getCjsj() );
        ksxtXxbDTO.setXgsj( entity.getXgsj() );

        return ksxtXxbDTO;
    }

    @Override
    public KsxtXxbDO convertToDO(KsxtXxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtXxbDO ksxtXxbDO = new KsxtXxbDO();

        ksxtXxbDO.setId( dto.getId() );
        ksxtXxbDO.setXxbt( dto.getXxbt() );
        ksxtXxbDO.setXxnr( dto.getXxnr() );
        ksxtXxbDO.setXxzt( dto.getXxzt() );
        ksxtXxbDO.setYxbz( dto.getYxbz() );
        ksxtXxbDO.setCjsj( dto.getCjsj() );
        ksxtXxbDO.setXgsj( dto.getXgsj() );

        return ksxtXxbDO;
    }

    @Override
    public KsxtXxbDTO convertToDTO(KsxtXxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtXxbDTO ksxtXxbDTO = new KsxtXxbDTO();

        ksxtXxbDTO.setXxbt( req.getXxbt() );
        ksxtXxbDTO.setXxnr( req.getXxnr() );
        ksxtXxbDTO.setXxzt( req.getXxzt() );
        ksxtXxbDTO.setYxbz( req.getYxbz() );
        ksxtXxbDTO.setCjsj( req.getCjsj() );
        ksxtXxbDTO.setXgsj( req.getXgsj() );

        return ksxtXxbDTO;
    }

    @Override
    public KsxtXxbDTO convertToDTO(KsxtXxbCreateReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtXxbDTO ksxtXxbDTO = new KsxtXxbDTO();

        ksxtXxbDTO.setXxbt( req.getXxbt() );
        ksxtXxbDTO.setXxnr( req.getXxnr() );
        ksxtXxbDTO.setXxzt( req.getXxzt() );

        return ksxtXxbDTO;
    }

    @Override
    public KsxtXxbDTO convertToDTO(KsxtXxbUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        KsxtXxbDTO ksxtXxbDTO = new KsxtXxbDTO();

        ksxtXxbDTO.setId( req.getId() );
        ksxtXxbDTO.setXxbt( req.getXxbt() );
        ksxtXxbDTO.setXxnr( req.getXxnr() );
        ksxtXxbDTO.setXxzt( req.getXxzt() );

        return ksxtXxbDTO;
    }

    @Override
    public KsxtXxbPageResp convertToPageResp(KsxtXxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtXxbPageResp ksxtXxbPageResp = new KsxtXxbPageResp();

        ksxtXxbPageResp.setId( dto.getId() );
        ksxtXxbPageResp.setXxbt( dto.getXxbt() );
        ksxtXxbPageResp.setXxnr( dto.getXxnr() );
        ksxtXxbPageResp.setXxzt( dto.getXxzt() );
        ksxtXxbPageResp.setCjsj( dto.getCjsj() );
        ksxtXxbPageResp.setXgsj( dto.getXgsj() );

        return ksxtXxbPageResp;
    }

    @Override
    public KsxtXxbViewResp convertToViewResp(KsxtXxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtXxbViewResp ksxtXxbViewResp = new KsxtXxbViewResp();

        ksxtXxbViewResp.setId( dto.getId() );
        ksxtXxbViewResp.setXxbt( dto.getXxbt() );
        ksxtXxbViewResp.setXxnr( dto.getXxnr() );
        ksxtXxbViewResp.setXxzt( dto.getXxzt() );
        ksxtXxbViewResp.setCjsj( dto.getCjsj() );
        ksxtXxbViewResp.setXgsj( dto.getXgsj() );

        return ksxtXxbViewResp;
    }

    @Override
    public KsxtXxbCreateResp convertToCreateResp(KsxtXxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        KsxtXxbCreateResp ksxtXxbCreateResp = new KsxtXxbCreateResp();

        ksxtXxbCreateResp.setId( dto.getId() );
        ksxtXxbCreateResp.setXxbt( dto.getXxbt() );
        ksxtXxbCreateResp.setXxnr( dto.getXxnr() );
        ksxtXxbCreateResp.setXxzt( dto.getXxzt() );

        return ksxtXxbCreateResp;
    }
}
