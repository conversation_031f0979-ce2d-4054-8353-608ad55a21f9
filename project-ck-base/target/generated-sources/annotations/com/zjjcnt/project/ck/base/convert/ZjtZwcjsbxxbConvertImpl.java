package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtZwcjsbxxbDTO;
import com.zjjcnt.project.ck.base.entity.ZjtZwcjsbxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtZwcjsbxxbConvertImpl implements ZjtZwcjsbxxbConvert {

    @Override
    public ZjtZwcjsbxxbDTO convert(ZjtZwcjsbxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtZwcjsbxxbDTO zjtZwcjsbxxbDTO = new ZjtZwcjsbxxbDTO();

        zjtZwcjsbxxbDTO.setId( entity.getId() );
        zjtZwcjsbxxbDTO.setSbid( entity.getSbid() );
        zjtZwcjsbxxbDTO.setZwcjqbsh( entity.getZwcjqbsh() );
        zjtZwcjsbxxbDTO.setZwqdxtzch( entity.getZwqdxtzch() );
        zjtZwcjsbxxbDTO.setSbmc( entity.getSbmc() );
        zjtZwcjsbxxbDTO.setPpxh( entity.getPpxh() );
        zjtZwcjsbxxbDTO.setDjsj( entity.getDjsj() );
        zjtZwcjsbxxbDTO.setSbdwgajgjgdm( entity.getSbdwgajgjgdm() );
        zjtZwcjsbxxbDTO.setSbdwgajgmc( entity.getSbdwgajgmc() );
        zjtZwcjsbxxbDTO.setSyztdm( entity.getSyztdm() );
        zjtZwcjsbxxbDTO.setQyrq( entity.getQyrq() );
        zjtZwcjsbxxbDTO.setTyrq( entity.getTyrq() );
        zjtZwcjsbxxbDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        zjtZwcjsbxxbDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        zjtZwcjsbxxbDTO.setBabz( entity.getBabz() );
        zjtZwcjsbxxbDTO.setBasj( entity.getBasj() );
        zjtZwcjsbxxbDTO.setFhdm( entity.getFhdm() );
        zjtZwcjsbxxbDTO.setFhms( entity.getFhms() );
        zjtZwcjsbxxbDTO.setSjbbh( entity.getSjbbh() );
        zjtZwcjsbxxbDTO.setRksj( entity.getRksj() );
        zjtZwcjsbxxbDTO.setJssjbbh( entity.getJssjbbh() );

        return zjtZwcjsbxxbDTO;
    }

    @Override
    public ZjtZwcjsbxxbDO convertToDO(ZjtZwcjsbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjsbxxbDO zjtZwcjsbxxbDO = new ZjtZwcjsbxxbDO();

        zjtZwcjsbxxbDO.setId( dto.getId() );
        zjtZwcjsbxxbDO.setSbid( dto.getSbid() );
        zjtZwcjsbxxbDO.setZwcjqbsh( dto.getZwcjqbsh() );
        zjtZwcjsbxxbDO.setZwqdxtzch( dto.getZwqdxtzch() );
        zjtZwcjsbxxbDO.setSbmc( dto.getSbmc() );
        zjtZwcjsbxxbDO.setPpxh( dto.getPpxh() );
        zjtZwcjsbxxbDO.setDjsj( dto.getDjsj() );
        zjtZwcjsbxxbDO.setSbdwgajgjgdm( dto.getSbdwgajgjgdm() );
        zjtZwcjsbxxbDO.setSbdwgajgmc( dto.getSbdwgajgmc() );
        zjtZwcjsbxxbDO.setSyztdm( dto.getSyztdm() );
        zjtZwcjsbxxbDO.setQyrq( dto.getQyrq() );
        zjtZwcjsbxxbDO.setTyrq( dto.getTyrq() );
        zjtZwcjsbxxbDO.setSjgsdwdm( dto.getSjgsdwdm() );
        zjtZwcjsbxxbDO.setSjgsdwmc( dto.getSjgsdwmc() );
        zjtZwcjsbxxbDO.setBabz( dto.getBabz() );
        zjtZwcjsbxxbDO.setBasj( dto.getBasj() );
        zjtZwcjsbxxbDO.setFhdm( dto.getFhdm() );
        zjtZwcjsbxxbDO.setFhms( dto.getFhms() );
        zjtZwcjsbxxbDO.setSjbbh( dto.getSjbbh() );
        zjtZwcjsbxxbDO.setRksj( dto.getRksj() );
        zjtZwcjsbxxbDO.setJssjbbh( dto.getJssjbbh() );

        return zjtZwcjsbxxbDO;
    }
}
