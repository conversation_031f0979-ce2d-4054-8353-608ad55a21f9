package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtLxSlxxbDTO;
import com.zjjcnt.project.ck.base.entity.ZjtLxSlxxbDO;
import java.util.Arrays;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:55+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtLxSlxxbConvertImpl implements ZjtLxSlxxbConvert {

    @Override
    public ZjtLxSlxxbDTO convert(ZjtLxSlxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtLxSlxxbDTO zjtLxSlxxbDTO = new ZjtLxSlxxbDTO();

        zjtLxSlxxbDTO.setId( entity.getId() );
        zjtLxSlxxbDTO.setNbslid( entity.getNbslid() );
        zjtLxSlxxbDTO.setYwslh( entity.getYwslh() );
        zjtLxSlxxbDTO.setNbsfzid( entity.getNbsfzid() );
        zjtLxSlxxbDTO.setZpid( entity.getZpid() );
        zjtLxSlxxbDTO.setSlh( entity.getSlh() );
        zjtLxSlxxbDTO.setRyid( entity.getRyid() );
        zjtLxSlxxbDTO.setRynbid( entity.getRynbid() );
        zjtLxSlxxbDTO.setQfjg( entity.getQfjg() );
        zjtLxSlxxbDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        zjtLxSlxxbDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        zjtLxSlxxbDTO.setZz( entity.getZz() );
        zjtLxSlxxbDTO.setSlyy( entity.getSlyy() );
        zjtLxSlxxbDTO.setZzlx( entity.getZzlx() );
        zjtLxSlxxbDTO.setLqfs( entity.getLqfs() );
        zjtLxSlxxbDTO.setSflx( entity.getSflx() );
        zjtLxSlxxbDTO.setSfje( entity.getSfje() );
        zjtLxSlxxbDTO.setSjblsh( entity.getSjblsh() );
        zjtLxSlxxbDTO.setSlzt( entity.getSlzt() );
        zjtLxSlxxbDTO.setTbbz( entity.getTbbz() );
        zjtLxSlxxbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtLxSlxxbDTO.setXm( entity.getXm() );
        zjtLxSlxxbDTO.setXb( entity.getXb() );
        zjtLxSlxxbDTO.setMz( entity.getMz() );
        zjtLxSlxxbDTO.setCsrq( entity.getCsrq() );
        zjtLxSlxxbDTO.setCsdssxq( entity.getCsdssxq() );
        zjtLxSlxxbDTO.setMlpnbid( entity.getMlpnbid() );
        zjtLxSlxxbDTO.setSsxq( entity.getSsxq() );
        zjtLxSlxxbDTO.setJlx( entity.getJlx() );
        zjtLxSlxxbDTO.setMlph( entity.getMlph() );
        zjtLxSlxxbDTO.setMlxz( entity.getMlxz() );
        zjtLxSlxxbDTO.setPcs( entity.getPcs() );
        zjtLxSlxxbDTO.setZrq( entity.getZrq() );
        zjtLxSlxxbDTO.setXzjd( entity.getXzjd() );
        zjtLxSlxxbDTO.setJcwh( entity.getJcwh() );
        zjtLxSlxxbDTO.setPxh( entity.getPxh() );
        zjtLxSlxxbDTO.setYwbz( entity.getYwbz() );
        zjtLxSlxxbDTO.setCzyid( entity.getCzyid() );
        zjtLxSlxxbDTO.setCzsj( entity.getCzsj() );
        zjtLxSlxxbDTO.setDwdm( entity.getDwdm() );
        zjtLxSlxxbDTO.setSjrxm( entity.getSjrxm() );
        zjtLxSlxxbDTO.setSjrlxdh( entity.getSjrlxdh() );
        zjtLxSlxxbDTO.setSjryb( entity.getSjryb() );
        zjtLxSlxxbDTO.setSjrssxq( entity.getSjrssxq() );
        zjtLxSlxxbDTO.setSjrxz( entity.getSjrxz() );
        zjtLxSlxxbDTO.setSjrtxdz( entity.getSjrtxdz() );
        zjtLxSlxxbDTO.setZzxxcwlb( entity.getZzxxcwlb() );
        zjtLxSlxxbDTO.setCwms( entity.getCwms() );
        zjtLxSlxxbDTO.setJydw( entity.getJydw() );
        zjtLxSlxxbDTO.setJyrxm( entity.getJyrxm() );
        zjtLxSlxxbDTO.setJyrq( entity.getJyrq() );
        zjtLxSlxxbDTO.setCldw( entity.getCldw() );
        zjtLxSlxxbDTO.setClqk( entity.getClqk() );
        zjtLxSlxxbDTO.setClrq( entity.getClrq() );
        zjtLxSlxxbDTO.setZlhkzt( entity.getZlhkzt() );
        zjtLxSlxxbDTO.setHksj( entity.getHksj() );
        zjtLxSlxxbDTO.setBwbha( entity.getBwbha() );
        zjtLxSlxxbDTO.setBwbhb( entity.getBwbhb() );
        zjtLxSlxxbDTO.setShrq( entity.getShrq() );
        zjtLxSlxxbDTO.setStjssj( entity.getStjssj() );
        zjtLxSlxxbDTO.setBwbhc( entity.getBwbhc() );
        zjtLxSlxxbDTO.setFjpch( entity.getFjpch() );
        zjtLxSlxxbDTO.setRlbdid( entity.getRlbdid() );
        zjtLxSlxxbDTO.setRlbdbz( entity.getRlbdbz() );
        zjtLxSlxxbDTO.setRlbdsj( entity.getRlbdsj() );
        zjtLxSlxxbDTO.setZwyzw( entity.getZwyzw() );
        zjtLxSlxxbDTO.setZwyzcjg( entity.getZwyzcjg() );
        zjtLxSlxxbDTO.setZwezw( entity.getZwezw() );
        zjtLxSlxxbDTO.setZwezcjg( entity.getZwezcjg() );
        zjtLxSlxxbDTO.setZwcjjgdm( entity.getZwcjjgdm() );
        zjtLxSlxxbDTO.setSzyczkdm( entity.getSzyczkdm() );
        zjtLxSlxxbDTO.setSfzwzj( entity.getSfzwzj() );
        zjtLxSlxxbDTO.setDztbbz( entity.getDztbbz() );
        zjtLxSlxxbDTO.setDztbsj( entity.getDztbsj() );
        zjtLxSlxxbDTO.setDzsjbbh( entity.getDzsjbbh() );
        zjtLxSlxxbDTO.setSlfs( entity.getSlfs() );
        zjtLxSlxxbDTO.setZwtxid( entity.getZwtxid() );
        zjtLxSlxxbDTO.setCzyxm( entity.getCzyxm() );
        zjtLxSlxxbDTO.setHjddzbm( entity.getHjddzbm() );
        zjtLxSlxxbDTO.setLssfzslbz( entity.getLssfzslbz() );
        zjtLxSlxxbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtLxSlxxbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtLxSlxxbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtLxSlxxbDTO.setZjzdssxq( entity.getZjzdssxq() );
        zjtLxSlxxbDTO.setZjzdxz( entity.getZjzdxz() );
        zjtLxSlxxbDTO.setLqrq( entity.getLqrq() );
        zjtLxSlxxbDTO.setLqrxm( entity.getLqrxm() );
        zjtLxSlxxbDTO.setLqrsfhm( entity.getLqrsfhm() );
        zjtLxSlxxbDTO.setLqrzpid( entity.getLqrzpid() );
        zjtLxSlxxbDTO.setZjddrq( entity.getZjddrq() );
        zjtLxSlxxbDTO.setLzczrid( entity.getLzczrid() );
        zjtLxSlxxbDTO.setLzczrxm( entity.getLzczrxm() );
        zjtLxSlxxbDTO.setShrxm( entity.getShrxm() );
        zjtLxSlxxbDTO.setShdw( entity.getShdw() );
        zjtLxSlxxbDTO.setShqk( entity.getShqk() );
        zjtLxSlxxbDTO.setQfrq( entity.getQfrq() );
        zjtLxSlxxbDTO.setQfrid( entity.getQfrid() );
        zjtLxSlxxbDTO.setQfrxm( entity.getQfrxm() );
        zjtLxSlxxbDTO.setQfdwjgdm( entity.getQfdwjgdm() );
        zjtLxSlxxbDTO.setQfdw( entity.getQfdw() );
        zjtLxSlxxbDTO.setDsshrq( entity.getDsshrq() );
        zjtLxSlxxbDTO.setDsshrxm( entity.getDsshrxm() );
        zjtLxSlxxbDTO.setDsshdw( entity.getDsshdw() );
        zjtLxSlxxbDTO.setDsshqk( entity.getDsshqk() );
        zjtLxSlxxbDTO.setRwid( entity.getRwid() );
        zjtLxSlxxbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtLxSlxxbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtLxSlxxbDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtLxSlxxbDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtLxSlxxbDTO.setShdwdm( entity.getShdwdm() );
        zjtLxSlxxbDTO.setCldwdm( entity.getCldwdm() );
        zjtLxSlxxbDTO.setDsshdwdm( entity.getDsshdwdm() );
        zjtLxSlxxbDTO.setShrid( entity.getShrid() );
        zjtLxSlxxbDTO.setDsshrid( entity.getDsshrid() );
        zjtLxSlxxbDTO.setSfdjh( entity.getSfdjh() );
        zjtLxSlxxbDTO.setRwzxrzbh( entity.getRwzxrzbh() );
        zjtLxSlxxbDTO.setRwddsj( entity.getRwddsj() );
        zjtLxSlxxbDTO.setSlyckrxsfbd( entity.getSlyckrxsfbd() );
        zjtLxSlxxbDTO.setRxbdkssj( entity.getRxbdkssj() );
        zjtLxSlxxbDTO.setRxbdhs( entity.getRxbdhs() );
        zjtLxSlxxbDTO.setRxbdxsd( entity.getRxbdxsd() );
        zjtLxSlxxbDTO.setRxbdkbh( entity.getRxbdkbh() );
        zjtLxSlxxbDTO.setRxbdjg( entity.getRxbdjg() );
        zjtLxSlxxbDTO.setSlylszwsfbd( entity.getSlylszwsfbd() );
        zjtLxSlxxbDTO.setZwybdjg( entity.getZwybdjg() );
        zjtLxSlxxbDTO.setZwybdxsd( entity.getZwybdxsd() );
        zjtLxSlxxbDTO.setZwebdjg( entity.getZwebdjg() );
        zjtLxSlxxbDTO.setZwebdxsd( entity.getZwebdxsd() );
        zjtLxSlxxbDTO.setLzszwsfhy( entity.getLzszwsfhy() );
        zjtLxSlxxbDTO.setLzszwyhyjg( entity.getLzszwyhyjg() );
        zjtLxSlxxbDTO.setLzszwyhyxsd( entity.getLzszwyhyxsd() );
        zjtLxSlxxbDTO.setLzszwehyjg( entity.getLzszwehyjg() );
        zjtLxSlxxbDTO.setLzszwehyxsd( entity.getLzszwehyxsd() );
        zjtLxSlxxbDTO.setLzssfjhjz( entity.getLzssfjhjz() );
        zjtLxSlxxbDTO.setSlylszwbdsm( entity.getSlylszwbdsm() );
        zjtLxSlxxbDTO.setLzszwbdsm( entity.getLzszwbdsm() );
        zjtLxSlxxbDTO.setSqrgmsfhm( entity.getSqrgmsfhm() );
        zjtLxSlxxbDTO.setSqrxm( entity.getSqrxm() );
        zjtLxSlxxbDTO.setSqrlxdh( entity.getSqrlxdh() );
        zjtLxSlxxbDTO.setJzqsrq( entity.getJzqsrq() );
        zjtLxSlxxbDTO.setCzylxdh( entity.getCzylxdh() );
        zjtLxSlxxbDTO.setShrlxdh( entity.getShrlxdh() );
        zjtLxSlxxbDTO.setDsshrlxdh( entity.getDsshrlxdh() );
        zjtLxSlxxbDTO.setZfcje( entity.getZfcje() );
        zjtLxSlxxbDTO.setQxfcje( entity.getQxfcje() );
        zjtLxSlxxbDTO.setDsfcje( entity.getDsfcje() );
        zjtLxSlxxbDTO.setZxfcje( entity.getZxfcje() );
        zjtLxSlxxbDTO.setYzkddh( entity.getYzkddh() );
        zjtLxSlxxbDTO.setSpdz1( entity.getSpdz1() );
        zjtLxSlxxbDTO.setSfmsbswzp( entity.getSfmsbswzp() );
        zjtLxSlxxbDTO.setHlwsqid( entity.getHlwsqid() );
        zjtLxSlxxbDTO.setUsername( entity.getUsername() );
        zjtLxSlxxbDTO.setPjjg( entity.getPjjg() );
        zjtLxSlxxbDTO.setPjpljc( entity.getPjpljc() );
        zjtLxSlxxbDTO.setPjsj( entity.getPjsj() );
        zjtLxSlxxbDTO.setFwdx( entity.getFwdx() );
        zjtLxSlxxbDTO.setSfdgszj( entity.getSfdgszj() );
        zjtLxSlxxbDTO.setBz( entity.getBz() );
        zjtLxSlxxbDTO.setSlshjdz( entity.getSlshjdz() );
        zjtLxSlxxbDTO.setKsywlsh( entity.getKsywlsh() );
        zjtLxSlxxbDTO.setKsfsbz( entity.getKsfsbz() );
        zjtLxSlxxbDTO.setKsfssj( entity.getKsfssj() );
        byte[] zp = entity.getZp();
        if ( zp != null ) {
            zjtLxSlxxbDTO.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        byte[] sqrzp = entity.getSqrzp();
        if ( sqrzp != null ) {
            zjtLxSlxxbDTO.setSqrzp( Arrays.copyOf( sqrzp, sqrzp.length ) );
        }
        byte[] zwxx = entity.getZwxx();
        if ( zwxx != null ) {
            zjtLxSlxxbDTO.setZwxx( Arrays.copyOf( zwxx, zwxx.length ) );
        }
        byte[] qzxx = entity.getQzxx();
        if ( qzxx != null ) {
            zjtLxSlxxbDTO.setQzxx( Arrays.copyOf( qzxx, qzxx.length ) );
        }
        byte[] hkb1 = entity.getHkb1();
        if ( hkb1 != null ) {
            zjtLxSlxxbDTO.setHkb1( Arrays.copyOf( hkb1, hkb1.length ) );
        }
        byte[] hkb2 = entity.getHkb2();
        if ( hkb2 != null ) {
            zjtLxSlxxbDTO.setHkb2( Arrays.copyOf( hkb2, hkb2.length ) );
        }
        byte[] hkb3 = entity.getHkb3();
        if ( hkb3 != null ) {
            zjtLxSlxxbDTO.setHkb3( Arrays.copyOf( hkb3, hkb3.length ) );
        }
        byte[] hkb4 = entity.getHkb4();
        if ( hkb4 != null ) {
            zjtLxSlxxbDTO.setHkb4( Arrays.copyOf( hkb4, hkb4.length ) );
        }
        byte[] sfz1 = entity.getSfz1();
        if ( sfz1 != null ) {
            zjtLxSlxxbDTO.setSfz1( Arrays.copyOf( sfz1, sfz1.length ) );
        }
        byte[] sfz2 = entity.getSfz2();
        if ( sfz2 != null ) {
            zjtLxSlxxbDTO.setSfz2( Arrays.copyOf( sfz2, sfz2.length ) );
        }
        byte[] sfz3 = entity.getSfz3();
        if ( sfz3 != null ) {
            zjtLxSlxxbDTO.setSfz3( Arrays.copyOf( sfz3, sfz3.length ) );
        }
        byte[] sfz4 = entity.getSfz4();
        if ( sfz4 != null ) {
            zjtLxSlxxbDTO.setSfz4( Arrays.copyOf( sfz4, sfz4.length ) );
        }
        zjtLxSlxxbDTO.setClsl( entity.getClsl() );

        return zjtLxSlxxbDTO;
    }

    @Override
    public ZjtLxSlxxbDO convertToDO(ZjtLxSlxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtLxSlxxbDO zjtLxSlxxbDO = new ZjtLxSlxxbDO();

        zjtLxSlxxbDO.setId( dto.getId() );
        zjtLxSlxxbDO.setNbslid( dto.getNbslid() );
        zjtLxSlxxbDO.setYwslh( dto.getYwslh() );
        zjtLxSlxxbDO.setNbsfzid( dto.getNbsfzid() );
        zjtLxSlxxbDO.setZpid( dto.getZpid() );
        zjtLxSlxxbDO.setSlh( dto.getSlh() );
        zjtLxSlxxbDO.setRyid( dto.getRyid() );
        zjtLxSlxxbDO.setRynbid( dto.getRynbid() );
        zjtLxSlxxbDO.setQfjg( dto.getQfjg() );
        zjtLxSlxxbDO.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtLxSlxxbDO.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtLxSlxxbDO.setZz( dto.getZz() );
        zjtLxSlxxbDO.setSlyy( dto.getSlyy() );
        zjtLxSlxxbDO.setZzlx( dto.getZzlx() );
        zjtLxSlxxbDO.setLqfs( dto.getLqfs() );
        zjtLxSlxxbDO.setSflx( dto.getSflx() );
        zjtLxSlxxbDO.setSfje( dto.getSfje() );
        zjtLxSlxxbDO.setSjblsh( dto.getSjblsh() );
        zjtLxSlxxbDO.setSlzt( dto.getSlzt() );
        zjtLxSlxxbDO.setTbbz( dto.getTbbz() );
        zjtLxSlxxbDO.setGmsfhm( dto.getGmsfhm() );
        zjtLxSlxxbDO.setXm( dto.getXm() );
        zjtLxSlxxbDO.setXb( dto.getXb() );
        zjtLxSlxxbDO.setMz( dto.getMz() );
        zjtLxSlxxbDO.setCsrq( dto.getCsrq() );
        zjtLxSlxxbDO.setCsdssxq( dto.getCsdssxq() );
        zjtLxSlxxbDO.setMlpnbid( dto.getMlpnbid() );
        zjtLxSlxxbDO.setSsxq( dto.getSsxq() );
        zjtLxSlxxbDO.setJlx( dto.getJlx() );
        zjtLxSlxxbDO.setMlph( dto.getMlph() );
        zjtLxSlxxbDO.setMlxz( dto.getMlxz() );
        zjtLxSlxxbDO.setPcs( dto.getPcs() );
        zjtLxSlxxbDO.setZrq( dto.getZrq() );
        zjtLxSlxxbDO.setXzjd( dto.getXzjd() );
        zjtLxSlxxbDO.setJcwh( dto.getJcwh() );
        zjtLxSlxxbDO.setPxh( dto.getPxh() );
        zjtLxSlxxbDO.setYwbz( dto.getYwbz() );
        zjtLxSlxxbDO.setCzyid( dto.getCzyid() );
        zjtLxSlxxbDO.setCzsj( dto.getCzsj() );
        zjtLxSlxxbDO.setDwdm( dto.getDwdm() );
        zjtLxSlxxbDO.setSjrxm( dto.getSjrxm() );
        zjtLxSlxxbDO.setSjrlxdh( dto.getSjrlxdh() );
        zjtLxSlxxbDO.setSjryb( dto.getSjryb() );
        zjtLxSlxxbDO.setSjrssxq( dto.getSjrssxq() );
        zjtLxSlxxbDO.setSjrxz( dto.getSjrxz() );
        zjtLxSlxxbDO.setSjrtxdz( dto.getSjrtxdz() );
        zjtLxSlxxbDO.setZzxxcwlb( dto.getZzxxcwlb() );
        zjtLxSlxxbDO.setCwms( dto.getCwms() );
        zjtLxSlxxbDO.setJydw( dto.getJydw() );
        zjtLxSlxxbDO.setJyrxm( dto.getJyrxm() );
        zjtLxSlxxbDO.setJyrq( dto.getJyrq() );
        zjtLxSlxxbDO.setCldw( dto.getCldw() );
        zjtLxSlxxbDO.setClqk( dto.getClqk() );
        zjtLxSlxxbDO.setClrq( dto.getClrq() );
        zjtLxSlxxbDO.setZlhkzt( dto.getZlhkzt() );
        zjtLxSlxxbDO.setHksj( dto.getHksj() );
        zjtLxSlxxbDO.setBwbha( dto.getBwbha() );
        zjtLxSlxxbDO.setBwbhb( dto.getBwbhb() );
        zjtLxSlxxbDO.setShrq( dto.getShrq() );
        zjtLxSlxxbDO.setStjssj( dto.getStjssj() );
        zjtLxSlxxbDO.setBwbhc( dto.getBwbhc() );
        zjtLxSlxxbDO.setFjpch( dto.getFjpch() );
        zjtLxSlxxbDO.setRlbdid( dto.getRlbdid() );
        zjtLxSlxxbDO.setRlbdbz( dto.getRlbdbz() );
        zjtLxSlxxbDO.setRlbdsj( dto.getRlbdsj() );
        zjtLxSlxxbDO.setZwyzw( dto.getZwyzw() );
        zjtLxSlxxbDO.setZwyzcjg( dto.getZwyzcjg() );
        zjtLxSlxxbDO.setZwezw( dto.getZwezw() );
        zjtLxSlxxbDO.setZwezcjg( dto.getZwezcjg() );
        zjtLxSlxxbDO.setZwcjjgdm( dto.getZwcjjgdm() );
        zjtLxSlxxbDO.setSzyczkdm( dto.getSzyczkdm() );
        zjtLxSlxxbDO.setSfzwzj( dto.getSfzwzj() );
        zjtLxSlxxbDO.setDztbbz( dto.getDztbbz() );
        zjtLxSlxxbDO.setDztbsj( dto.getDztbsj() );
        zjtLxSlxxbDO.setDzsjbbh( dto.getDzsjbbh() );
        zjtLxSlxxbDO.setSlfs( dto.getSlfs() );
        zjtLxSlxxbDO.setZwtxid( dto.getZwtxid() );
        zjtLxSlxxbDO.setCzyxm( dto.getCzyxm() );
        zjtLxSlxxbDO.setHjddzbm( dto.getHjddzbm() );
        zjtLxSlxxbDO.setLssfzslbz( dto.getLssfzslbz() );
        zjtLxSlxxbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtLxSlxxbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtLxSlxxbDO.setCzydwmc( dto.getCzydwmc() );
        zjtLxSlxxbDO.setZjzdssxq( dto.getZjzdssxq() );
        zjtLxSlxxbDO.setZjzdxz( dto.getZjzdxz() );
        zjtLxSlxxbDO.setLqrq( dto.getLqrq() );
        zjtLxSlxxbDO.setLqrxm( dto.getLqrxm() );
        zjtLxSlxxbDO.setLqrsfhm( dto.getLqrsfhm() );
        zjtLxSlxxbDO.setLqrzpid( dto.getLqrzpid() );
        zjtLxSlxxbDO.setZjddrq( dto.getZjddrq() );
        zjtLxSlxxbDO.setLzczrid( dto.getLzczrid() );
        zjtLxSlxxbDO.setLzczrxm( dto.getLzczrxm() );
        zjtLxSlxxbDO.setShrxm( dto.getShrxm() );
        zjtLxSlxxbDO.setShdw( dto.getShdw() );
        zjtLxSlxxbDO.setShqk( dto.getShqk() );
        zjtLxSlxxbDO.setQfrq( dto.getQfrq() );
        zjtLxSlxxbDO.setQfrid( dto.getQfrid() );
        zjtLxSlxxbDO.setQfrxm( dto.getQfrxm() );
        zjtLxSlxxbDO.setQfdwjgdm( dto.getQfdwjgdm() );
        zjtLxSlxxbDO.setQfdw( dto.getQfdw() );
        zjtLxSlxxbDO.setDsshrq( dto.getDsshrq() );
        zjtLxSlxxbDO.setDsshrxm( dto.getDsshrxm() );
        zjtLxSlxxbDO.setDsshdw( dto.getDsshdw() );
        zjtLxSlxxbDO.setDsshqk( dto.getDsshqk() );
        zjtLxSlxxbDO.setRwid( dto.getRwid() );
        zjtLxSlxxbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtLxSlxxbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtLxSlxxbDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtLxSlxxbDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtLxSlxxbDO.setShdwdm( dto.getShdwdm() );
        zjtLxSlxxbDO.setCldwdm( dto.getCldwdm() );
        zjtLxSlxxbDO.setDsshdwdm( dto.getDsshdwdm() );
        zjtLxSlxxbDO.setShrid( dto.getShrid() );
        zjtLxSlxxbDO.setDsshrid( dto.getDsshrid() );
        zjtLxSlxxbDO.setSfdjh( dto.getSfdjh() );
        zjtLxSlxxbDO.setRwzxrzbh( dto.getRwzxrzbh() );
        zjtLxSlxxbDO.setRwddsj( dto.getRwddsj() );
        zjtLxSlxxbDO.setSlyckrxsfbd( dto.getSlyckrxsfbd() );
        zjtLxSlxxbDO.setRxbdkssj( dto.getRxbdkssj() );
        zjtLxSlxxbDO.setRxbdhs( dto.getRxbdhs() );
        zjtLxSlxxbDO.setRxbdxsd( dto.getRxbdxsd() );
        zjtLxSlxxbDO.setRxbdkbh( dto.getRxbdkbh() );
        zjtLxSlxxbDO.setRxbdjg( dto.getRxbdjg() );
        zjtLxSlxxbDO.setSlylszwsfbd( dto.getSlylszwsfbd() );
        zjtLxSlxxbDO.setZwybdjg( dto.getZwybdjg() );
        zjtLxSlxxbDO.setZwybdxsd( dto.getZwybdxsd() );
        zjtLxSlxxbDO.setZwebdjg( dto.getZwebdjg() );
        zjtLxSlxxbDO.setZwebdxsd( dto.getZwebdxsd() );
        zjtLxSlxxbDO.setLzszwsfhy( dto.getLzszwsfhy() );
        zjtLxSlxxbDO.setLzszwyhyjg( dto.getLzszwyhyjg() );
        zjtLxSlxxbDO.setLzszwyhyxsd( dto.getLzszwyhyxsd() );
        zjtLxSlxxbDO.setLzszwehyjg( dto.getLzszwehyjg() );
        zjtLxSlxxbDO.setLzszwehyxsd( dto.getLzszwehyxsd() );
        zjtLxSlxxbDO.setLzssfjhjz( dto.getLzssfjhjz() );
        zjtLxSlxxbDO.setSlylszwbdsm( dto.getSlylszwbdsm() );
        zjtLxSlxxbDO.setLzszwbdsm( dto.getLzszwbdsm() );
        zjtLxSlxxbDO.setSqrgmsfhm( dto.getSqrgmsfhm() );
        zjtLxSlxxbDO.setSqrxm( dto.getSqrxm() );
        zjtLxSlxxbDO.setSqrlxdh( dto.getSqrlxdh() );
        zjtLxSlxxbDO.setJzqsrq( dto.getJzqsrq() );
        zjtLxSlxxbDO.setCzylxdh( dto.getCzylxdh() );
        zjtLxSlxxbDO.setShrlxdh( dto.getShrlxdh() );
        zjtLxSlxxbDO.setDsshrlxdh( dto.getDsshrlxdh() );
        zjtLxSlxxbDO.setZfcje( dto.getZfcje() );
        zjtLxSlxxbDO.setQxfcje( dto.getQxfcje() );
        zjtLxSlxxbDO.setDsfcje( dto.getDsfcje() );
        zjtLxSlxxbDO.setZxfcje( dto.getZxfcje() );
        zjtLxSlxxbDO.setYzkddh( dto.getYzkddh() );
        zjtLxSlxxbDO.setSpdz1( dto.getSpdz1() );
        zjtLxSlxxbDO.setSfmsbswzp( dto.getSfmsbswzp() );
        zjtLxSlxxbDO.setHlwsqid( dto.getHlwsqid() );
        zjtLxSlxxbDO.setUsername( dto.getUsername() );
        zjtLxSlxxbDO.setPjjg( dto.getPjjg() );
        zjtLxSlxxbDO.setPjpljc( dto.getPjpljc() );
        zjtLxSlxxbDO.setPjsj( dto.getPjsj() );
        zjtLxSlxxbDO.setFwdx( dto.getFwdx() );
        zjtLxSlxxbDO.setSfdgszj( dto.getSfdgszj() );
        zjtLxSlxxbDO.setBz( dto.getBz() );
        zjtLxSlxxbDO.setSlshjdz( dto.getSlshjdz() );
        zjtLxSlxxbDO.setKsywlsh( dto.getKsywlsh() );
        zjtLxSlxxbDO.setKsfsbz( dto.getKsfsbz() );
        zjtLxSlxxbDO.setKsfssj( dto.getKsfssj() );
        byte[] zp = dto.getZp();
        if ( zp != null ) {
            zjtLxSlxxbDO.setZp( Arrays.copyOf( zp, zp.length ) );
        }
        byte[] sqrzp = dto.getSqrzp();
        if ( sqrzp != null ) {
            zjtLxSlxxbDO.setSqrzp( Arrays.copyOf( sqrzp, sqrzp.length ) );
        }
        byte[] zwxx = dto.getZwxx();
        if ( zwxx != null ) {
            zjtLxSlxxbDO.setZwxx( Arrays.copyOf( zwxx, zwxx.length ) );
        }
        byte[] qzxx = dto.getQzxx();
        if ( qzxx != null ) {
            zjtLxSlxxbDO.setQzxx( Arrays.copyOf( qzxx, qzxx.length ) );
        }
        byte[] hkb1 = dto.getHkb1();
        if ( hkb1 != null ) {
            zjtLxSlxxbDO.setHkb1( Arrays.copyOf( hkb1, hkb1.length ) );
        }
        byte[] hkb2 = dto.getHkb2();
        if ( hkb2 != null ) {
            zjtLxSlxxbDO.setHkb2( Arrays.copyOf( hkb2, hkb2.length ) );
        }
        byte[] hkb3 = dto.getHkb3();
        if ( hkb3 != null ) {
            zjtLxSlxxbDO.setHkb3( Arrays.copyOf( hkb3, hkb3.length ) );
        }
        byte[] hkb4 = dto.getHkb4();
        if ( hkb4 != null ) {
            zjtLxSlxxbDO.setHkb4( Arrays.copyOf( hkb4, hkb4.length ) );
        }
        byte[] sfz1 = dto.getSfz1();
        if ( sfz1 != null ) {
            zjtLxSlxxbDO.setSfz1( Arrays.copyOf( sfz1, sfz1.length ) );
        }
        byte[] sfz2 = dto.getSfz2();
        if ( sfz2 != null ) {
            zjtLxSlxxbDO.setSfz2( Arrays.copyOf( sfz2, sfz2.length ) );
        }
        byte[] sfz3 = dto.getSfz3();
        if ( sfz3 != null ) {
            zjtLxSlxxbDO.setSfz3( Arrays.copyOf( sfz3, sfz3.length ) );
        }
        byte[] sfz4 = dto.getSfz4();
        if ( sfz4 != null ) {
            zjtLxSlxxbDO.setSfz4( Arrays.copyOf( sfz4, sfz4.length ) );
        }
        zjtLxSlxxbDO.setClsl( dto.getClsl() );

        return zjtLxSlxxbDO;
    }
}
