package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtBzsflsbDTO;
import com.zjjcnt.project.ck.base.dto.ZjtSlxxbDTO;
import com.zjjcnt.project.ck.base.entity.ZjtBzsflsbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtBzsflsbConvertImpl implements ZjtBzsflsbConvert {

    @Override
    public ZjtBzsflsbDTO convert(ZjtBzsflsbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtBzsflsbDTO zjtBzsflsbDTO = new ZjtBzsflsbDTO();

        zjtBzsflsbDTO.setId( entity.getId() );
        zjtBzsflsbDTO.setSflsid( entity.getSflsid() );
        zjtBzsflsbDTO.setNbslid( entity.getNbslid() );
        zjtBzsflsbDTO.setYwslh( entity.getYwslh() );
        zjtBzsflsbDTO.setSlh( entity.getSlh() );
        zjtBzsflsbDTO.setRyid( entity.getRyid() );
        zjtBzsflsbDTO.setRynbid( entity.getRynbid() );
        zjtBzsflsbDTO.setGmsfhm( entity.getGmsfhm() );
        zjtBzsflsbDTO.setXm( entity.getXm() );
        zjtBzsflsbDTO.setSlyy( entity.getSlyy() );
        zjtBzsflsbDTO.setSlzt( entity.getSlzt() );
        zjtBzsflsbDTO.setSfje( entity.getSfje() );
        zjtBzsflsbDTO.setQfjg( entity.getQfjg() );
        zjtBzsflsbDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        zjtBzsflsbDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        zjtBzsflsbDTO.setSflx( entity.getSflx() );
        zjtBzsflsbDTO.setSlfs( entity.getSlfs() );
        zjtBzsflsbDTO.setHjdsjgsdwdm( entity.getHjdsjgsdwdm() );
        zjtBzsflsbDTO.setSsxq( entity.getSsxq() );
        zjtBzsflsbDTO.setHjdsjgsdwmc( entity.getHjdsjgsdwmc() );
        zjtBzsflsbDTO.setSldsjgsdwdm( entity.getSldsjgsdwdm() );
        zjtBzsflsbDTO.setSldsjgsdwmc( entity.getSldsjgsdwmc() );
        zjtBzsflsbDTO.setSldfjsjgsdwdm( entity.getSldfjsjgsdwdm() );
        zjtBzsflsbDTO.setSldfjsjgsdwmc( entity.getSldfjsjgsdwmc() );
        zjtBzsflsbDTO.setCzsj( entity.getCzsj() );
        zjtBzsflsbDTO.setCzyid( entity.getCzyid() );
        zjtBzsflsbDTO.setCzyxm( entity.getCzyxm() );
        zjtBzsflsbDTO.setDwdm( entity.getDwdm() );
        zjtBzsflsbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtBzsflsbDTO.setSfdjh( entity.getSfdjh() );
        zjtBzsflsbDTO.setBz( entity.getBz() );
        zjtBzsflsbDTO.setZfcje( entity.getZfcje() );
        zjtBzsflsbDTO.setQxfcje( entity.getQxfcje() );
        zjtBzsflsbDTO.setDsfcje( entity.getDsfcje() );
        zjtBzsflsbDTO.setZxfcje( entity.getZxfcje() );

        return zjtBzsflsbDTO;
    }

    @Override
    public ZjtBzsflsbDO convertToDO(ZjtBzsflsbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtBzsflsbDO zjtBzsflsbDO = new ZjtBzsflsbDO();

        zjtBzsflsbDO.setId( dto.getId() );
        zjtBzsflsbDO.setSflsid( dto.getSflsid() );
        zjtBzsflsbDO.setNbslid( dto.getNbslid() );
        zjtBzsflsbDO.setYwslh( dto.getYwslh() );
        zjtBzsflsbDO.setSlh( dto.getSlh() );
        zjtBzsflsbDO.setRyid( dto.getRyid() );
        zjtBzsflsbDO.setRynbid( dto.getRynbid() );
        zjtBzsflsbDO.setGmsfhm( dto.getGmsfhm() );
        zjtBzsflsbDO.setXm( dto.getXm() );
        zjtBzsflsbDO.setSlyy( dto.getSlyy() );
        zjtBzsflsbDO.setSlzt( dto.getSlzt() );
        zjtBzsflsbDO.setSfje( dto.getSfje() );
        zjtBzsflsbDO.setQfjg( dto.getQfjg() );
        zjtBzsflsbDO.setYxqxqsrq( dto.getYxqxqsrq() );
        zjtBzsflsbDO.setYxqxjzrq( dto.getYxqxjzrq() );
        zjtBzsflsbDO.setSflx( dto.getSflx() );
        zjtBzsflsbDO.setSlfs( dto.getSlfs() );
        zjtBzsflsbDO.setHjdsjgsdwdm( dto.getHjdsjgsdwdm() );
        zjtBzsflsbDO.setSsxq( dto.getSsxq() );
        zjtBzsflsbDO.setHjdsjgsdwmc( dto.getHjdsjgsdwmc() );
        zjtBzsflsbDO.setSldsjgsdwdm( dto.getSldsjgsdwdm() );
        zjtBzsflsbDO.setSldsjgsdwmc( dto.getSldsjgsdwmc() );
        zjtBzsflsbDO.setSldfjsjgsdwdm( dto.getSldfjsjgsdwdm() );
        zjtBzsflsbDO.setSldfjsjgsdwmc( dto.getSldfjsjgsdwmc() );
        zjtBzsflsbDO.setCzsj( dto.getCzsj() );
        zjtBzsflsbDO.setCzyid( dto.getCzyid() );
        zjtBzsflsbDO.setCzyxm( dto.getCzyxm() );
        zjtBzsflsbDO.setDwdm( dto.getDwdm() );
        zjtBzsflsbDO.setCzydwmc( dto.getCzydwmc() );
        zjtBzsflsbDO.setSfdjh( dto.getSfdjh() );
        zjtBzsflsbDO.setBz( dto.getBz() );
        zjtBzsflsbDO.setZfcje( dto.getZfcje() );
        zjtBzsflsbDO.setQxfcje( dto.getQxfcje() );
        zjtBzsflsbDO.setDsfcje( dto.getDsfcje() );
        zjtBzsflsbDO.setZxfcje( dto.getZxfcje() );

        return zjtBzsflsbDO;
    }

    @Override
    public ZjtBzsflsbDTO convertToDTO(ZjtSlxxbDTO zjtSlxxb) {
        if ( zjtSlxxb == null ) {
            return null;
        }

        ZjtBzsflsbDTO zjtBzsflsbDTO = new ZjtBzsflsbDTO();

        zjtBzsflsbDTO.setNbslid( zjtSlxxb.getNbslid() );
        zjtBzsflsbDTO.setYwslh( zjtSlxxb.getYwslh() );
        zjtBzsflsbDTO.setSlh( zjtSlxxb.getSlh() );
        zjtBzsflsbDTO.setRyid( zjtSlxxb.getRyid() );
        zjtBzsflsbDTO.setRynbid( zjtSlxxb.getRynbid() );
        zjtBzsflsbDTO.setGmsfhm( zjtSlxxb.getGmsfhm() );
        zjtBzsflsbDTO.setXm( zjtSlxxb.getXm() );
        zjtBzsflsbDTO.setSlyy( zjtSlxxb.getSlyy() );
        zjtBzsflsbDTO.setSlzt( zjtSlxxb.getSlzt() );
        zjtBzsflsbDTO.setSfje( zjtSlxxb.getSfje() );
        zjtBzsflsbDTO.setQfjg( zjtSlxxb.getQfjg() );
        zjtBzsflsbDTO.setYxqxqsrq( zjtSlxxb.getYxqxqsrq() );
        zjtBzsflsbDTO.setYxqxjzrq( zjtSlxxb.getYxqxjzrq() );
        zjtBzsflsbDTO.setSflx( zjtSlxxb.getSflx() );
        zjtBzsflsbDTO.setSlfs( zjtSlxxb.getSlfs() );
        zjtBzsflsbDTO.setHjdsjgsdwdm( zjtSlxxb.getHjdsjgsdwdm() );
        zjtBzsflsbDTO.setSsxq( zjtSlxxb.getSsxq() );
        zjtBzsflsbDTO.setHjdsjgsdwmc( zjtSlxxb.getHjdsjgsdwmc() );
        zjtBzsflsbDTO.setSldsjgsdwdm( zjtSlxxb.getSldsjgsdwdm() );
        zjtBzsflsbDTO.setSldsjgsdwmc( zjtSlxxb.getSldsjgsdwmc() );
        zjtBzsflsbDTO.setSldfjsjgsdwdm( zjtSlxxb.getSldfjsjgsdwdm() );
        zjtBzsflsbDTO.setSldfjsjgsdwmc( zjtSlxxb.getSldfjsjgsdwmc() );
        zjtBzsflsbDTO.setCzsj( zjtSlxxb.getCzsj() );
        zjtBzsflsbDTO.setCzyid( zjtSlxxb.getCzyid() );
        zjtBzsflsbDTO.setCzyxm( zjtSlxxb.getCzyxm() );
        zjtBzsflsbDTO.setDwdm( zjtSlxxb.getDwdm() );
        zjtBzsflsbDTO.setCzydwmc( zjtSlxxb.getCzydwmc() );
        zjtBzsflsbDTO.setSfdjh( zjtSlxxb.getSfdjh() );
        zjtBzsflsbDTO.setBz( zjtSlxxb.getBz() );
        zjtBzsflsbDTO.setZfcje( zjtSlxxb.getZfcje() );
        zjtBzsflsbDTO.setQxfcje( zjtSlxxb.getQxfcje() );
        zjtBzsflsbDTO.setDsfcje( zjtSlxxb.getDsfcje() );
        zjtBzsflsbDTO.setZxfcje( zjtSlxxb.getZxfcje() );
        zjtBzsflsbDTO.setYxqxqsrqStart( zjtSlxxb.getYxqxqsrqStart() );
        zjtBzsflsbDTO.setYxqxqsrqEnd( zjtSlxxb.getYxqxqsrqEnd() );
        zjtBzsflsbDTO.setYxqxjzrqStart( zjtSlxxb.getYxqxjzrqStart() );
        zjtBzsflsbDTO.setYxqxjzrqEnd( zjtSlxxb.getYxqxjzrqEnd() );
        zjtBzsflsbDTO.setCzsjStart( zjtSlxxb.getCzsjStart() );
        zjtBzsflsbDTO.setCzsjEnd( zjtSlxxb.getCzsjEnd() );

        return zjtBzsflsbDTO;
    }
}
