package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.HjxxCzrkjbxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.HjxxCzrkjbxxbCreateReq;
import com.zjjcnt.project.ck.base.dto.req.HjxxCzrkjbxxbPageReq;
import com.zjjcnt.project.ck.base.dto.req.HjxxCzrkjbxxbUpdateReq;
import com.zjjcnt.project.ck.base.dto.resp.HjxxCzrkjbxxbCreateResp;
import com.zjjcnt.project.ck.base.dto.resp.HjxxCzrkjbxxbPageResp;
import com.zjjcnt.project.ck.base.dto.resp.HjxxCzrkjbxxbViewQgResp;
import com.zjjcnt.project.ck.base.dto.resp.HjxxCzrkjbxxbViewResp;
import com.zjjcnt.project.ck.base.entity.HjxxCzrkjbxxbDO;
import com.zjjcnt.project.ck.jk.ws.ZalwcxqqHjgljbxxResultVo;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:56+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class HjxxCzrkjbxxbConvertImpl implements HjxxCzrkjbxxbConvert {

    @Override
    public HjxxCzrkjbxxbDTO convert(HjxxCzrkjbxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = new HjxxCzrkjbxxbDTO();

        hjxxCzrkjbxxbDTO.setId( entity.getId() );
        hjxxCzrkjbxxbDTO.setRynbid( entity.getRynbid() );
        hjxxCzrkjbxxbDTO.setRyid( entity.getRyid() );
        hjxxCzrkjbxxbDTO.setHhnbid( entity.getHhnbid() );
        hjxxCzrkjbxxbDTO.setMlpnbid( entity.getMlpnbid() );
        hjxxCzrkjbxxbDTO.setZpid( entity.getZpid() );
        hjxxCzrkjbxxbDTO.setNbsfzid( entity.getNbsfzid() );
        hjxxCzrkjbxxbDTO.setGmsfhm( entity.getGmsfhm() );
        hjxxCzrkjbxxbDTO.setQfjg( entity.getQfjg() );
        hjxxCzrkjbxxbDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        hjxxCzrkjbxxbDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        hjxxCzrkjbxxbDTO.setXm( entity.getXm() );
        hjxxCzrkjbxxbDTO.setCym( entity.getCym() );
        hjxxCzrkjbxxbDTO.setXmpy( entity.getXmpy() );
        hjxxCzrkjbxxbDTO.setCympy( entity.getCympy() );
        hjxxCzrkjbxxbDTO.setXb( entity.getXb() );
        hjxxCzrkjbxxbDTO.setMz( entity.getMz() );
        hjxxCzrkjbxxbDTO.setCsrq( entity.getCsrq() );
        hjxxCzrkjbxxbDTO.setCssj( entity.getCssj() );
        hjxxCzrkjbxxbDTO.setCsdgjdq( entity.getCsdgjdq() );
        hjxxCzrkjbxxbDTO.setCsdssxq( entity.getCsdssxq() );
        hjxxCzrkjbxxbDTO.setCsdxz( entity.getCsdxz() );
        hjxxCzrkjbxxbDTO.setDhhm( entity.getDhhm() );
        hjxxCzrkjbxxbDTO.setJhryxm( entity.getJhryxm() );
        hjxxCzrkjbxxbDTO.setJhrygmsfhm( entity.getJhrygmsfhm() );
        hjxxCzrkjbxxbDTO.setJhryjhgx( entity.getJhryjhgx() );
        hjxxCzrkjbxxbDTO.setJhrexm( entity.getJhrexm() );
        hjxxCzrkjbxxbDTO.setJhregmsfhm( entity.getJhregmsfhm() );
        hjxxCzrkjbxxbDTO.setJhrejhgx( entity.getJhrejhgx() );
        hjxxCzrkjbxxbDTO.setFqxm( entity.getFqxm() );
        hjxxCzrkjbxxbDTO.setFqgmsfhm( entity.getFqgmsfhm() );
        hjxxCzrkjbxxbDTO.setMqxm( entity.getMqxm() );
        hjxxCzrkjbxxbDTO.setMqgmsfhm( entity.getMqgmsfhm() );
        hjxxCzrkjbxxbDTO.setPoxm( entity.getPoxm() );
        hjxxCzrkjbxxbDTO.setPogmsfhm( entity.getPogmsfhm() );
        hjxxCzrkjbxxbDTO.setJggjdq( entity.getJggjdq() );
        hjxxCzrkjbxxbDTO.setJgssxq( entity.getJgssxq() );
        hjxxCzrkjbxxbDTO.setZjxy( entity.getZjxy() );
        hjxxCzrkjbxxbDTO.setWhcd( entity.getWhcd() );
        hjxxCzrkjbxxbDTO.setHyzk( entity.getHyzk() );
        hjxxCzrkjbxxbDTO.setByzk( entity.getByzk() );
        hjxxCzrkjbxxbDTO.setSg( entity.getSg() );
        hjxxCzrkjbxxbDTO.setXx( entity.getXx() );
        hjxxCzrkjbxxbDTO.setZy( entity.getZy() );
        hjxxCzrkjbxxbDTO.setZylb( entity.getZylb() );
        hjxxCzrkjbxxbDTO.setFwcs( entity.getFwcs() );
        hjxxCzrkjbxxbDTO.setXxjb( entity.getXxjb() );
        hjxxCzrkjbxxbDTO.setHsql( entity.getHsql() );
        hjxxCzrkjbxxbDTO.setHyql( entity.getHyql() );
        hjxxCzrkjbxxbDTO.setHgjdqql( entity.getHgjdqql() );
        hjxxCzrkjbxxbDTO.setHssxqql( entity.getHssxqql() );
        hjxxCzrkjbxxbDTO.setHxzql( entity.getHxzql() );
        hjxxCzrkjbxxbDTO.setHslbz( entity.getHslbz() );
        hjxxCzrkjbxxbDTO.setHylbz( entity.getHylbz() );
        hjxxCzrkjbxxbDTO.setHgjdqlbz( entity.getHgjdqlbz() );
        hjxxCzrkjbxxbDTO.setHsssqlbz( entity.getHsssqlbz() );
        hjxxCzrkjbxxbDTO.setHxzlbz( entity.getHxzlbz() );
        hjxxCzrkjbxxbDTO.setSwrq( entity.getSwrq() );
        hjxxCzrkjbxxbDTO.setSwzxlb( entity.getSwzxlb() );
        hjxxCzrkjbxxbDTO.setSwzxrq( entity.getSwzxrq() );
        hjxxCzrkjbxxbDTO.setQcrq( entity.getQcrq() );
        hjxxCzrkjbxxbDTO.setQczxlb( entity.getQczxlb() );
        hjxxCzrkjbxxbDTO.setQwdgjdq( entity.getQwdgjdq() );
        hjxxCzrkjbxxbDTO.setQwdssxq( entity.getQwdssxq() );
        hjxxCzrkjbxxbDTO.setQwdxz( entity.getQwdxz() );
        hjxxCzrkjbxxbDTO.setCszmbh( entity.getCszmbh() );
        hjxxCzrkjbxxbDTO.setCszqfrq( entity.getCszqfrq() );
        hjxxCzrkjbxxbDTO.setHylb( entity.getHylb() );
        hjxxCzrkjbxxbDTO.setQtssxq( entity.getQtssxq() );
        hjxxCzrkjbxxbDTO.setQtzz( entity.getQtzz() );
        hjxxCzrkjbxxbDTO.setRylb( entity.getRylb() );
        hjxxCzrkjbxxbDTO.setHb( entity.getHb() );
        hjxxCzrkjbxxbDTO.setYhzgx( entity.getYhzgx() );
        hjxxCzrkjbxxbDTO.setRyzt( entity.getRyzt() );
        hjxxCzrkjbxxbDTO.setRysdzt( entity.getRysdzt() );
        hjxxCzrkjbxxbDTO.setLxdbid( entity.getLxdbid() );
        hjxxCzrkjbxxbDTO.setBz( entity.getBz() );
        hjxxCzrkjbxxbDTO.setJlbz( entity.getJlbz() );
        hjxxCzrkjbxxbDTO.setYwnr( entity.getYwnr() );
        hjxxCzrkjbxxbDTO.setCjhjywid( entity.getCjhjywid() );
        hjxxCzrkjbxxbDTO.setCchjywid( entity.getCchjywid() );
        hjxxCzrkjbxxbDTO.setQysj( entity.getQysj() );
        hjxxCzrkjbxxbDTO.setJssj( entity.getJssj() );
        hjxxCzrkjbxxbDTO.setCxbz( entity.getCxbz() );
        hjxxCzrkjbxxbDTO.setZjlb( entity.getZjlb() );
        hjxxCzrkjbxxbDTO.setJlx( entity.getJlx() );
        hjxxCzrkjbxxbDTO.setMlph( entity.getMlph() );
        hjxxCzrkjbxxbDTO.setMlxz( entity.getMlxz() );
        hjxxCzrkjbxxbDTO.setPcs( entity.getPcs() );
        hjxxCzrkjbxxbDTO.setZrq( entity.getZrq() );
        hjxxCzrkjbxxbDTO.setXzjd( entity.getXzjd() );
        hjxxCzrkjbxxbDTO.setJcwh( entity.getJcwh() );
        hjxxCzrkjbxxbDTO.setPxh( entity.getPxh() );
        hjxxCzrkjbxxbDTO.setMlpid( entity.getMlpid() );
        hjxxCzrkjbxxbDTO.setSsxq( entity.getSsxq() );
        hjxxCzrkjbxxbDTO.setHh( entity.getHh() );
        hjxxCzrkjbxxbDTO.setHlx( entity.getHlx() );
        hjxxCzrkjbxxbDTO.setHhid( entity.getHhid() );
        hjxxCzrkjbxxbDTO.setBdfw( entity.getBdfw() );
        hjxxCzrkjbxxbDTO.setXxqysj( entity.getXxqysj() );
        hjxxCzrkjbxxbDTO.setDhhm2( entity.getDhhm2() );
        hjxxCzrkjbxxbDTO.setXzdcjzt( entity.getXzdcjzt() );
        hjxxCzrkjbxxbDTO.setZwyzw( entity.getZwyzw() );
        hjxxCzrkjbxxbDTO.setZwyzcjg( entity.getZwyzcjg() );
        hjxxCzrkjbxxbDTO.setZwezw( entity.getZwezw() );
        hjxxCzrkjbxxbDTO.setZwezcjg( entity.getZwezcjg() );
        hjxxCzrkjbxxbDTO.setZwcjjgdm( entity.getZwcjjgdm() );
        hjxxCzrkjbxxbDTO.setSzyczkdm( entity.getSzyczkdm() );
        hjxxCzrkjbxxbDTO.setX( entity.getX() );
        hjxxCzrkjbxxbDTO.setM( entity.getM() );
        hjxxCzrkjbxxbDTO.setJgxz( entity.getJgxz() );
        hjxxCzrkjbxxbDTO.setJhrycyzjdm( entity.getJhrycyzjdm() );
        hjxxCzrkjbxxbDTO.setJhryzjhm( entity.getJhryzjhm() );
        hjxxCzrkjbxxbDTO.setJhrywwx( entity.getJhrywwx() );
        hjxxCzrkjbxxbDTO.setJhrywwm( entity.getJhrywwm() );
        hjxxCzrkjbxxbDTO.setJhrylxdh( entity.getJhrylxdh() );
        hjxxCzrkjbxxbDTO.setJhrecyzjdm( entity.getJhrecyzjdm() );
        hjxxCzrkjbxxbDTO.setJhrezjhm( entity.getJhrezjhm() );
        hjxxCzrkjbxxbDTO.setJhrewwx( entity.getJhrewwx() );
        hjxxCzrkjbxxbDTO.setJhrewwm( entity.getJhrewwm() );
        hjxxCzrkjbxxbDTO.setJhrelxdh( entity.getJhrelxdh() );
        hjxxCzrkjbxxbDTO.setFqcyzjdm( entity.getFqcyzjdm() );
        hjxxCzrkjbxxbDTO.setFqzjhm( entity.getFqzjhm() );
        hjxxCzrkjbxxbDTO.setFqwwx( entity.getFqwwx() );
        hjxxCzrkjbxxbDTO.setFqwwm( entity.getFqwwm() );
        hjxxCzrkjbxxbDTO.setMqcyzjdm( entity.getMqcyzjdm() );
        hjxxCzrkjbxxbDTO.setMqzjhm( entity.getMqzjhm() );
        hjxxCzrkjbxxbDTO.setMqwwx( entity.getMqwwx() );
        hjxxCzrkjbxxbDTO.setMqwwm( entity.getMqwwm() );
        hjxxCzrkjbxxbDTO.setPocyzjdm( entity.getPocyzjdm() );
        hjxxCzrkjbxxbDTO.setPozjhm( entity.getPozjhm() );
        hjxxCzrkjbxxbDTO.setPowwx( entity.getPowwx() );
        hjxxCzrkjbxxbDTO.setPowwm( entity.getPowwm() );
        hjxxCzrkjbxxbDTO.setCyzkdwbm( entity.getCyzkdwbm() );
        hjxxCzrkjbxxbDTO.setCyzkdwmc( entity.getCyzkdwmc() );
        hjxxCzrkjbxxbDTO.setHqyldyy( entity.getHqyldyy() );
        hjxxCzrkjbxxbDTO.setSwyy( entity.getSwyy() );
        hjxxCzrkjbxxbDTO.setQcqyldyy( entity.getQcqyldyy() );
        hjxxCzrkjbxxbDTO.setZxsj( entity.getZxsj() );
        hjxxCzrkjbxxbDTO.setGxsj( entity.getGxsj() );
        hjxxCzrkjbxxbDTO.setSjgsdwdm( entity.getSjgsdwdm() );
        hjxxCzrkjbxxbDTO.setSjgsdwmc( entity.getSjgsdwmc() );
        hjxxCzrkjbxxbDTO.setHjddzbm( entity.getHjddzbm() );
        hjxxCzrkjbxxbDTO.setHjdssxq( entity.getHjdssxq() );
        hjxxCzrkjbxxbDTO.setHjdxxdz( entity.getHjdxxdz() );
        hjxxCzrkjbxxbDTO.setHjdrhyzbs( entity.getHjdrhyzbs() );
        hjxxCzrkjbxxbDTO.setJzddzbm( entity.getJzddzbm() );
        hjxxCzrkjbxxbDTO.setJzdssxq( entity.getJzdssxq() );
        hjxxCzrkjbxxbDTO.setJzdxxdz( entity.getJzdxxdz() );
        hjxxCzrkjbxxbDTO.setZjdz( entity.getZjdz() );
        hjxxCzrkjbxxbDTO.setHqyldyylbz( entity.getHqyldyylbz() );
        hjxxCzrkjbxxbDTO.setDjsj( entity.getDjsj() );
        hjxxCzrkjbxxbDTO.setDjyy( entity.getDjyy() );
        hjxxCzrkjbxxbDTO.setJcdjsj( entity.getJcdjsj() );
        hjxxCzrkjbxxbDTO.setHkdjid( entity.getHkdjid() );
        hjxxCzrkjbxxbDTO.setDjzt( entity.getDjzt() );
        hjxxCzrkjbxxbDTO.setTjyxzqh( entity.getTjyxzqh() );
        hjxxCzrkjbxxbDTO.setCxsx( entity.getCxsx() );

        return hjxxCzrkjbxxbDTO;
    }

    @Override
    public HjxxCzrkjbxxbDO convertToDO(HjxxCzrkjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxCzrkjbxxbDO hjxxCzrkjbxxbDO = new HjxxCzrkjbxxbDO();

        hjxxCzrkjbxxbDO.setId( dto.getId() );
        hjxxCzrkjbxxbDO.setRynbid( dto.getRynbid() );
        hjxxCzrkjbxxbDO.setRyid( dto.getRyid() );
        hjxxCzrkjbxxbDO.setHhnbid( dto.getHhnbid() );
        hjxxCzrkjbxxbDO.setMlpnbid( dto.getMlpnbid() );
        hjxxCzrkjbxxbDO.setZpid( dto.getZpid() );
        hjxxCzrkjbxxbDO.setNbsfzid( dto.getNbsfzid() );
        hjxxCzrkjbxxbDO.setGmsfhm( dto.getGmsfhm() );
        hjxxCzrkjbxxbDO.setQfjg( dto.getQfjg() );
        hjxxCzrkjbxxbDO.setYxqxqsrq( dto.getYxqxqsrq() );
        hjxxCzrkjbxxbDO.setYxqxjzrq( dto.getYxqxjzrq() );
        hjxxCzrkjbxxbDO.setXm( dto.getXm() );
        hjxxCzrkjbxxbDO.setCym( dto.getCym() );
        hjxxCzrkjbxxbDO.setXmpy( dto.getXmpy() );
        hjxxCzrkjbxxbDO.setCympy( dto.getCympy() );
        hjxxCzrkjbxxbDO.setXb( dto.getXb() );
        hjxxCzrkjbxxbDO.setMz( dto.getMz() );
        hjxxCzrkjbxxbDO.setCsrq( dto.getCsrq() );
        hjxxCzrkjbxxbDO.setCssj( dto.getCssj() );
        hjxxCzrkjbxxbDO.setCsdgjdq( dto.getCsdgjdq() );
        hjxxCzrkjbxxbDO.setCsdssxq( dto.getCsdssxq() );
        hjxxCzrkjbxxbDO.setCsdxz( dto.getCsdxz() );
        hjxxCzrkjbxxbDO.setDhhm( dto.getDhhm() );
        hjxxCzrkjbxxbDO.setJhryxm( dto.getJhryxm() );
        hjxxCzrkjbxxbDO.setJhrygmsfhm( dto.getJhrygmsfhm() );
        hjxxCzrkjbxxbDO.setJhryjhgx( dto.getJhryjhgx() );
        hjxxCzrkjbxxbDO.setJhrexm( dto.getJhrexm() );
        hjxxCzrkjbxxbDO.setJhregmsfhm( dto.getJhregmsfhm() );
        hjxxCzrkjbxxbDO.setJhrejhgx( dto.getJhrejhgx() );
        hjxxCzrkjbxxbDO.setFqxm( dto.getFqxm() );
        hjxxCzrkjbxxbDO.setFqgmsfhm( dto.getFqgmsfhm() );
        hjxxCzrkjbxxbDO.setMqxm( dto.getMqxm() );
        hjxxCzrkjbxxbDO.setMqgmsfhm( dto.getMqgmsfhm() );
        hjxxCzrkjbxxbDO.setPoxm( dto.getPoxm() );
        hjxxCzrkjbxxbDO.setPogmsfhm( dto.getPogmsfhm() );
        hjxxCzrkjbxxbDO.setJggjdq( dto.getJggjdq() );
        hjxxCzrkjbxxbDO.setJgssxq( dto.getJgssxq() );
        hjxxCzrkjbxxbDO.setZjxy( dto.getZjxy() );
        hjxxCzrkjbxxbDO.setWhcd( dto.getWhcd() );
        hjxxCzrkjbxxbDO.setHyzk( dto.getHyzk() );
        hjxxCzrkjbxxbDO.setByzk( dto.getByzk() );
        hjxxCzrkjbxxbDO.setSg( dto.getSg() );
        hjxxCzrkjbxxbDO.setXx( dto.getXx() );
        hjxxCzrkjbxxbDO.setZy( dto.getZy() );
        hjxxCzrkjbxxbDO.setZylb( dto.getZylb() );
        hjxxCzrkjbxxbDO.setFwcs( dto.getFwcs() );
        hjxxCzrkjbxxbDO.setXxjb( dto.getXxjb() );
        hjxxCzrkjbxxbDO.setHsql( dto.getHsql() );
        hjxxCzrkjbxxbDO.setHyql( dto.getHyql() );
        hjxxCzrkjbxxbDO.setHgjdqql( dto.getHgjdqql() );
        hjxxCzrkjbxxbDO.setHssxqql( dto.getHssxqql() );
        hjxxCzrkjbxxbDO.setHxzql( dto.getHxzql() );
        hjxxCzrkjbxxbDO.setHslbz( dto.getHslbz() );
        hjxxCzrkjbxxbDO.setHylbz( dto.getHylbz() );
        hjxxCzrkjbxxbDO.setHgjdqlbz( dto.getHgjdqlbz() );
        hjxxCzrkjbxxbDO.setHsssqlbz( dto.getHsssqlbz() );
        hjxxCzrkjbxxbDO.setHxzlbz( dto.getHxzlbz() );
        hjxxCzrkjbxxbDO.setSwrq( dto.getSwrq() );
        hjxxCzrkjbxxbDO.setSwzxlb( dto.getSwzxlb() );
        hjxxCzrkjbxxbDO.setSwzxrq( dto.getSwzxrq() );
        hjxxCzrkjbxxbDO.setQcrq( dto.getQcrq() );
        hjxxCzrkjbxxbDO.setQczxlb( dto.getQczxlb() );
        hjxxCzrkjbxxbDO.setQwdgjdq( dto.getQwdgjdq() );
        hjxxCzrkjbxxbDO.setQwdssxq( dto.getQwdssxq() );
        hjxxCzrkjbxxbDO.setQwdxz( dto.getQwdxz() );
        hjxxCzrkjbxxbDO.setCszmbh( dto.getCszmbh() );
        hjxxCzrkjbxxbDO.setCszqfrq( dto.getCszqfrq() );
        hjxxCzrkjbxxbDO.setHylb( dto.getHylb() );
        hjxxCzrkjbxxbDO.setQtssxq( dto.getQtssxq() );
        hjxxCzrkjbxxbDO.setQtzz( dto.getQtzz() );
        hjxxCzrkjbxxbDO.setRylb( dto.getRylb() );
        hjxxCzrkjbxxbDO.setHb( dto.getHb() );
        hjxxCzrkjbxxbDO.setYhzgx( dto.getYhzgx() );
        hjxxCzrkjbxxbDO.setRyzt( dto.getRyzt() );
        hjxxCzrkjbxxbDO.setRysdzt( dto.getRysdzt() );
        if ( dto.getLxdbid() != null ) {
            hjxxCzrkjbxxbDO.setLxdbid( dto.getLxdbid() );
        }
        hjxxCzrkjbxxbDO.setBz( dto.getBz() );
        hjxxCzrkjbxxbDO.setJlbz( dto.getJlbz() );
        hjxxCzrkjbxxbDO.setYwnr( dto.getYwnr() );
        if ( dto.getCjhjywid() != null ) {
            hjxxCzrkjbxxbDO.setCjhjywid( dto.getCjhjywid() );
        }
        if ( dto.getCchjywid() != null ) {
            hjxxCzrkjbxxbDO.setCchjywid( dto.getCchjywid() );
        }
        hjxxCzrkjbxxbDO.setQysj( dto.getQysj() );
        hjxxCzrkjbxxbDO.setJssj( dto.getJssj() );
        hjxxCzrkjbxxbDO.setCxbz( dto.getCxbz() );
        hjxxCzrkjbxxbDO.setZjlb( dto.getZjlb() );
        hjxxCzrkjbxxbDO.setJlx( dto.getJlx() );
        hjxxCzrkjbxxbDO.setMlph( dto.getMlph() );
        hjxxCzrkjbxxbDO.setMlxz( dto.getMlxz() );
        hjxxCzrkjbxxbDO.setPcs( dto.getPcs() );
        hjxxCzrkjbxxbDO.setZrq( dto.getZrq() );
        hjxxCzrkjbxxbDO.setXzjd( dto.getXzjd() );
        hjxxCzrkjbxxbDO.setJcwh( dto.getJcwh() );
        hjxxCzrkjbxxbDO.setPxh( dto.getPxh() );
        if ( dto.getMlpid() != null ) {
            hjxxCzrkjbxxbDO.setMlpid( dto.getMlpid() );
        }
        hjxxCzrkjbxxbDO.setSsxq( dto.getSsxq() );
        hjxxCzrkjbxxbDO.setHh( dto.getHh() );
        hjxxCzrkjbxxbDO.setHlx( dto.getHlx() );
        if ( dto.getHhid() != null ) {
            hjxxCzrkjbxxbDO.setHhid( dto.getHhid() );
        }
        hjxxCzrkjbxxbDO.setBdfw( dto.getBdfw() );
        hjxxCzrkjbxxbDO.setXxqysj( dto.getXxqysj() );
        hjxxCzrkjbxxbDO.setDhhm2( dto.getDhhm2() );
        hjxxCzrkjbxxbDO.setXzdcjzt( dto.getXzdcjzt() );
        hjxxCzrkjbxxbDO.setZwyzw( dto.getZwyzw() );
        hjxxCzrkjbxxbDO.setZwyzcjg( dto.getZwyzcjg() );
        hjxxCzrkjbxxbDO.setZwezw( dto.getZwezw() );
        hjxxCzrkjbxxbDO.setZwezcjg( dto.getZwezcjg() );
        hjxxCzrkjbxxbDO.setZwcjjgdm( dto.getZwcjjgdm() );
        hjxxCzrkjbxxbDO.setSzyczkdm( dto.getSzyczkdm() );
        hjxxCzrkjbxxbDO.setX( dto.getX() );
        hjxxCzrkjbxxbDO.setM( dto.getM() );
        hjxxCzrkjbxxbDO.setJgxz( dto.getJgxz() );
        hjxxCzrkjbxxbDO.setJhrycyzjdm( dto.getJhrycyzjdm() );
        hjxxCzrkjbxxbDO.setJhryzjhm( dto.getJhryzjhm() );
        hjxxCzrkjbxxbDO.setJhrywwx( dto.getJhrywwx() );
        hjxxCzrkjbxxbDO.setJhrywwm( dto.getJhrywwm() );
        hjxxCzrkjbxxbDO.setJhrylxdh( dto.getJhrylxdh() );
        hjxxCzrkjbxxbDO.setJhrecyzjdm( dto.getJhrecyzjdm() );
        hjxxCzrkjbxxbDO.setJhrezjhm( dto.getJhrezjhm() );
        hjxxCzrkjbxxbDO.setJhrewwx( dto.getJhrewwx() );
        hjxxCzrkjbxxbDO.setJhrewwm( dto.getJhrewwm() );
        hjxxCzrkjbxxbDO.setJhrelxdh( dto.getJhrelxdh() );
        hjxxCzrkjbxxbDO.setFqcyzjdm( dto.getFqcyzjdm() );
        hjxxCzrkjbxxbDO.setFqzjhm( dto.getFqzjhm() );
        hjxxCzrkjbxxbDO.setFqwwx( dto.getFqwwx() );
        hjxxCzrkjbxxbDO.setFqwwm( dto.getFqwwm() );
        hjxxCzrkjbxxbDO.setMqcyzjdm( dto.getMqcyzjdm() );
        hjxxCzrkjbxxbDO.setMqzjhm( dto.getMqzjhm() );
        hjxxCzrkjbxxbDO.setMqwwx( dto.getMqwwx() );
        hjxxCzrkjbxxbDO.setMqwwm( dto.getMqwwm() );
        hjxxCzrkjbxxbDO.setPocyzjdm( dto.getPocyzjdm() );
        hjxxCzrkjbxxbDO.setPozjhm( dto.getPozjhm() );
        hjxxCzrkjbxxbDO.setPowwx( dto.getPowwx() );
        hjxxCzrkjbxxbDO.setPowwm( dto.getPowwm() );
        hjxxCzrkjbxxbDO.setCyzkdwbm( dto.getCyzkdwbm() );
        hjxxCzrkjbxxbDO.setCyzkdwmc( dto.getCyzkdwmc() );
        hjxxCzrkjbxxbDO.setHqyldyy( dto.getHqyldyy() );
        hjxxCzrkjbxxbDO.setSwyy( dto.getSwyy() );
        hjxxCzrkjbxxbDO.setQcqyldyy( dto.getQcqyldyy() );
        hjxxCzrkjbxxbDO.setZxsj( dto.getZxsj() );
        hjxxCzrkjbxxbDO.setGxsj( dto.getGxsj() );
        hjxxCzrkjbxxbDO.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxCzrkjbxxbDO.setSjgsdwmc( dto.getSjgsdwmc() );
        hjxxCzrkjbxxbDO.setHjddzbm( dto.getHjddzbm() );
        hjxxCzrkjbxxbDO.setHjdssxq( dto.getHjdssxq() );
        hjxxCzrkjbxxbDO.setHjdxxdz( dto.getHjdxxdz() );
        hjxxCzrkjbxxbDO.setHjdrhyzbs( dto.getHjdrhyzbs() );
        hjxxCzrkjbxxbDO.setJzddzbm( dto.getJzddzbm() );
        hjxxCzrkjbxxbDO.setJzdssxq( dto.getJzdssxq() );
        hjxxCzrkjbxxbDO.setJzdxxdz( dto.getJzdxxdz() );
        hjxxCzrkjbxxbDO.setZjdz( dto.getZjdz() );
        hjxxCzrkjbxxbDO.setHqyldyylbz( dto.getHqyldyylbz() );
        hjxxCzrkjbxxbDO.setDjsj( dto.getDjsj() );
        hjxxCzrkjbxxbDO.setDjyy( dto.getDjyy() );
        hjxxCzrkjbxxbDO.setJcdjsj( dto.getJcdjsj() );
        hjxxCzrkjbxxbDO.setHkdjid( dto.getHkdjid() );
        hjxxCzrkjbxxbDO.setDjzt( dto.getDjzt() );
        hjxxCzrkjbxxbDO.setTjyxzqh( dto.getTjyxzqh() );
        hjxxCzrkjbxxbDO.setCxsx( dto.getCxsx() );

        return hjxxCzrkjbxxbDO;
    }

    @Override
    public HjxxCzrkjbxxbDTO convertToDTO(HjxxCzrkjbxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = new HjxxCzrkjbxxbDTO();

        hjxxCzrkjbxxbDTO.setRyid( req.getRyid() );
        hjxxCzrkjbxxbDTO.setHhnbid( req.getHhnbid() );
        hjxxCzrkjbxxbDTO.setMlpnbid( req.getMlpnbid() );
        hjxxCzrkjbxxbDTO.setZpid( req.getZpid() );
        hjxxCzrkjbxxbDTO.setNbsfzid( req.getNbsfzid() );
        hjxxCzrkjbxxbDTO.setGmsfhm( req.getGmsfhm() );
        hjxxCzrkjbxxbDTO.setQfjg( req.getQfjg() );
        hjxxCzrkjbxxbDTO.setYxqxqsrq( req.getYxqxqsrq() );
        hjxxCzrkjbxxbDTO.setYxqxjzrq( req.getYxqxjzrq() );
        hjxxCzrkjbxxbDTO.setXm( req.getXm() );
        hjxxCzrkjbxxbDTO.setCym( req.getCym() );
        hjxxCzrkjbxxbDTO.setXmpy( req.getXmpy() );
        hjxxCzrkjbxxbDTO.setCympy( req.getCympy() );
        hjxxCzrkjbxxbDTO.setXb( req.getXb() );
        hjxxCzrkjbxxbDTO.setMz( req.getMz() );
        hjxxCzrkjbxxbDTO.setCsrq( req.getCsrq() );
        hjxxCzrkjbxxbDTO.setCssj( req.getCssj() );
        hjxxCzrkjbxxbDTO.setCsdgjdq( req.getCsdgjdq() );
        hjxxCzrkjbxxbDTO.setCsdssxq( req.getCsdssxq() );
        hjxxCzrkjbxxbDTO.setCsdxz( req.getCsdxz() );
        hjxxCzrkjbxxbDTO.setDhhm( req.getDhhm() );
        hjxxCzrkjbxxbDTO.setJhryxm( req.getJhryxm() );
        hjxxCzrkjbxxbDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        hjxxCzrkjbxxbDTO.setJhryjhgx( req.getJhryjhgx() );
        hjxxCzrkjbxxbDTO.setJhrexm( req.getJhrexm() );
        hjxxCzrkjbxxbDTO.setJhregmsfhm( req.getJhregmsfhm() );
        hjxxCzrkjbxxbDTO.setJhrejhgx( req.getJhrejhgx() );
        hjxxCzrkjbxxbDTO.setFqxm( req.getFqxm() );
        hjxxCzrkjbxxbDTO.setFqgmsfhm( req.getFqgmsfhm() );
        hjxxCzrkjbxxbDTO.setMqxm( req.getMqxm() );
        hjxxCzrkjbxxbDTO.setMqgmsfhm( req.getMqgmsfhm() );
        hjxxCzrkjbxxbDTO.setPoxm( req.getPoxm() );
        hjxxCzrkjbxxbDTO.setPogmsfhm( req.getPogmsfhm() );
        hjxxCzrkjbxxbDTO.setJggjdq( req.getJggjdq() );
        hjxxCzrkjbxxbDTO.setJgssxq( req.getJgssxq() );
        hjxxCzrkjbxxbDTO.setZjxy( req.getZjxy() );
        hjxxCzrkjbxxbDTO.setWhcd( req.getWhcd() );
        hjxxCzrkjbxxbDTO.setHyzk( req.getHyzk() );
        hjxxCzrkjbxxbDTO.setByzk( req.getByzk() );
        hjxxCzrkjbxxbDTO.setSg( req.getSg() );
        hjxxCzrkjbxxbDTO.setXx( req.getXx() );
        hjxxCzrkjbxxbDTO.setZy( req.getZy() );
        hjxxCzrkjbxxbDTO.setZylb( req.getZylb() );
        hjxxCzrkjbxxbDTO.setFwcs( req.getFwcs() );
        hjxxCzrkjbxxbDTO.setXxjb( req.getXxjb() );
        hjxxCzrkjbxxbDTO.setHsql( req.getHsql() );
        hjxxCzrkjbxxbDTO.setHyql( req.getHyql() );
        hjxxCzrkjbxxbDTO.setHgjdqql( req.getHgjdqql() );
        hjxxCzrkjbxxbDTO.setHssxqql( req.getHssxqql() );
        hjxxCzrkjbxxbDTO.setHxzql( req.getHxzql() );
        hjxxCzrkjbxxbDTO.setHslbz( req.getHslbz() );
        hjxxCzrkjbxxbDTO.setHylbz( req.getHylbz() );
        hjxxCzrkjbxxbDTO.setHgjdqlbz( req.getHgjdqlbz() );
        hjxxCzrkjbxxbDTO.setHsssqlbz( req.getHsssqlbz() );
        hjxxCzrkjbxxbDTO.setHxzlbz( req.getHxzlbz() );
        hjxxCzrkjbxxbDTO.setSwrq( req.getSwrq() );
        hjxxCzrkjbxxbDTO.setSwzxlb( req.getSwzxlb() );
        hjxxCzrkjbxxbDTO.setSwzxrq( req.getSwzxrq() );
        hjxxCzrkjbxxbDTO.setQcrq( req.getQcrq() );
        hjxxCzrkjbxxbDTO.setQczxlb( req.getQczxlb() );
        hjxxCzrkjbxxbDTO.setQwdgjdq( req.getQwdgjdq() );
        hjxxCzrkjbxxbDTO.setQwdssxq( req.getQwdssxq() );
        hjxxCzrkjbxxbDTO.setQwdxz( req.getQwdxz() );
        hjxxCzrkjbxxbDTO.setCszmbh( req.getCszmbh() );
        hjxxCzrkjbxxbDTO.setCszqfrq( req.getCszqfrq() );
        hjxxCzrkjbxxbDTO.setHylb( req.getHylb() );
        hjxxCzrkjbxxbDTO.setQtssxq( req.getQtssxq() );
        hjxxCzrkjbxxbDTO.setQtzz( req.getQtzz() );
        hjxxCzrkjbxxbDTO.setRylb( req.getRylb() );
        hjxxCzrkjbxxbDTO.setHb( req.getHb() );
        hjxxCzrkjbxxbDTO.setYhzgx( req.getYhzgx() );
        hjxxCzrkjbxxbDTO.setRyzt( req.getRyzt() );
        hjxxCzrkjbxxbDTO.setRysdzt( req.getRysdzt() );
        hjxxCzrkjbxxbDTO.setLxdbid( req.getLxdbid() );
        hjxxCzrkjbxxbDTO.setBz( req.getBz() );
        hjxxCzrkjbxxbDTO.setJlbz( req.getJlbz() );
        hjxxCzrkjbxxbDTO.setYwnr( req.getYwnr() );
        hjxxCzrkjbxxbDTO.setCjhjywid( req.getCjhjywid() );
        hjxxCzrkjbxxbDTO.setCchjywid( req.getCchjywid() );
        hjxxCzrkjbxxbDTO.setQysj( req.getQysj() );
        hjxxCzrkjbxxbDTO.setJssj( req.getJssj() );
        hjxxCzrkjbxxbDTO.setCxbz( req.getCxbz() );
        hjxxCzrkjbxxbDTO.setZjlb( req.getZjlb() );
        hjxxCzrkjbxxbDTO.setJlx( req.getJlx() );
        hjxxCzrkjbxxbDTO.setMlph( req.getMlph() );
        hjxxCzrkjbxxbDTO.setMlxz( req.getMlxz() );
        hjxxCzrkjbxxbDTO.setPcs( req.getPcs() );
        hjxxCzrkjbxxbDTO.setZrq( req.getZrq() );
        hjxxCzrkjbxxbDTO.setXzjd( req.getXzjd() );
        hjxxCzrkjbxxbDTO.setJcwh( req.getJcwh() );
        hjxxCzrkjbxxbDTO.setPxh( req.getPxh() );
        hjxxCzrkjbxxbDTO.setMlpid( req.getMlpid() );
        hjxxCzrkjbxxbDTO.setSsxq( req.getSsxq() );
        hjxxCzrkjbxxbDTO.setHh( req.getHh() );
        hjxxCzrkjbxxbDTO.setHlx( req.getHlx() );
        hjxxCzrkjbxxbDTO.setHhid( req.getHhid() );
        hjxxCzrkjbxxbDTO.setBdfw( req.getBdfw() );
        hjxxCzrkjbxxbDTO.setXxqysj( req.getXxqysj() );
        hjxxCzrkjbxxbDTO.setDhhm2( req.getDhhm2() );
        hjxxCzrkjbxxbDTO.setXzdcjzt( req.getXzdcjzt() );
        hjxxCzrkjbxxbDTO.setZwyzw( req.getZwyzw() );
        hjxxCzrkjbxxbDTO.setZwyzcjg( req.getZwyzcjg() );
        hjxxCzrkjbxxbDTO.setZwezw( req.getZwezw() );
        hjxxCzrkjbxxbDTO.setZwezcjg( req.getZwezcjg() );
        hjxxCzrkjbxxbDTO.setZwcjjgdm( req.getZwcjjgdm() );
        hjxxCzrkjbxxbDTO.setSzyczkdm( req.getSzyczkdm() );
        hjxxCzrkjbxxbDTO.setX( req.getX() );
        hjxxCzrkjbxxbDTO.setM( req.getM() );
        hjxxCzrkjbxxbDTO.setJgxz( req.getJgxz() );
        hjxxCzrkjbxxbDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        hjxxCzrkjbxxbDTO.setJhryzjhm( req.getJhryzjhm() );
        hjxxCzrkjbxxbDTO.setJhrywwx( req.getJhrywwx() );
        hjxxCzrkjbxxbDTO.setJhrywwm( req.getJhrywwm() );
        hjxxCzrkjbxxbDTO.setJhrylxdh( req.getJhrylxdh() );
        hjxxCzrkjbxxbDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        hjxxCzrkjbxxbDTO.setJhrezjhm( req.getJhrezjhm() );
        hjxxCzrkjbxxbDTO.setJhrewwx( req.getJhrewwx() );
        hjxxCzrkjbxxbDTO.setJhrewwm( req.getJhrewwm() );
        hjxxCzrkjbxxbDTO.setJhrelxdh( req.getJhrelxdh() );
        hjxxCzrkjbxxbDTO.setFqcyzjdm( req.getFqcyzjdm() );
        hjxxCzrkjbxxbDTO.setFqzjhm( req.getFqzjhm() );
        hjxxCzrkjbxxbDTO.setFqwwx( req.getFqwwx() );
        hjxxCzrkjbxxbDTO.setFqwwm( req.getFqwwm() );
        hjxxCzrkjbxxbDTO.setMqcyzjdm( req.getMqcyzjdm() );
        hjxxCzrkjbxxbDTO.setMqzjhm( req.getMqzjhm() );
        hjxxCzrkjbxxbDTO.setMqwwx( req.getMqwwx() );
        hjxxCzrkjbxxbDTO.setMqwwm( req.getMqwwm() );
        hjxxCzrkjbxxbDTO.setPocyzjdm( req.getPocyzjdm() );
        hjxxCzrkjbxxbDTO.setPozjhm( req.getPozjhm() );
        hjxxCzrkjbxxbDTO.setPowwx( req.getPowwx() );
        hjxxCzrkjbxxbDTO.setPowwm( req.getPowwm() );
        hjxxCzrkjbxxbDTO.setCyzkdwbm( req.getCyzkdwbm() );
        hjxxCzrkjbxxbDTO.setCyzkdwmc( req.getCyzkdwmc() );
        hjxxCzrkjbxxbDTO.setHqyldyy( req.getHqyldyy() );
        hjxxCzrkjbxxbDTO.setSwyy( req.getSwyy() );
        hjxxCzrkjbxxbDTO.setQcqyldyy( req.getQcqyldyy() );
        hjxxCzrkjbxxbDTO.setZxsj( req.getZxsj() );
        hjxxCzrkjbxxbDTO.setGxsj( req.getGxsj() );
        hjxxCzrkjbxxbDTO.setSjgsdwdm( req.getSjgsdwdm() );
        hjxxCzrkjbxxbDTO.setSjgsdwmc( req.getSjgsdwmc() );
        hjxxCzrkjbxxbDTO.setHjddzbm( req.getHjddzbm() );
        hjxxCzrkjbxxbDTO.setHjdssxq( req.getHjdssxq() );
        hjxxCzrkjbxxbDTO.setHjdxxdz( req.getHjdxxdz() );
        hjxxCzrkjbxxbDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        hjxxCzrkjbxxbDTO.setJzddzbm( req.getJzddzbm() );
        hjxxCzrkjbxxbDTO.setJzdssxq( req.getJzdssxq() );
        hjxxCzrkjbxxbDTO.setJzdxxdz( req.getJzdxxdz() );
        hjxxCzrkjbxxbDTO.setZjdz( req.getZjdz() );
        hjxxCzrkjbxxbDTO.setHqyldyylbz( req.getHqyldyylbz() );
        hjxxCzrkjbxxbDTO.setDjsj( req.getDjsj() );
        hjxxCzrkjbxxbDTO.setDjyy( req.getDjyy() );
        hjxxCzrkjbxxbDTO.setJcdjsj( req.getJcdjsj() );
        hjxxCzrkjbxxbDTO.setHkdjid( req.getHkdjid() );
        hjxxCzrkjbxxbDTO.setDjzt( req.getDjzt() );
        hjxxCzrkjbxxbDTO.setTjyxzqh( req.getTjyxzqh() );
        hjxxCzrkjbxxbDTO.setCxsx( req.getCxsx() );
        hjxxCzrkjbxxbDTO.setNlStart( req.getNlStart() );
        hjxxCzrkjbxxbDTO.setNlEnd( req.getNlEnd() );
        hjxxCzrkjbxxbDTO.setSearchCzrk( req.getSearchCzrk() );
        hjxxCzrkjbxxbDTO.setSearchSwrk( req.getSearchSwrk() );
        hjxxCzrkjbxxbDTO.setSearchQclx( req.getSearchQclx() );
        hjxxCzrkjbxxbDTO.setQclx( req.getQclx() );
        hjxxCzrkjbxxbDTO.setSearchQtrk( req.getSearchQtrk() );
        hjxxCzrkjbxxbDTO.setSearchLsjl( req.getSearchLsjl() );
        hjxxCzrkjbxxbDTO.setCxssxq( req.getCxssxq() );

        return hjxxCzrkjbxxbDTO;
    }

    @Override
    public HjxxCzrkjbxxbDTO convertToDTO(HjxxCzrkjbxxbCreateReq req) {
        if ( req == null ) {
            return null;
        }

        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = new HjxxCzrkjbxxbDTO();

        hjxxCzrkjbxxbDTO.setRyid( req.getRyid() );
        hjxxCzrkjbxxbDTO.setHhnbid( req.getHhnbid() );
        hjxxCzrkjbxxbDTO.setMlpnbid( req.getMlpnbid() );
        hjxxCzrkjbxxbDTO.setZpid( req.getZpid() );
        hjxxCzrkjbxxbDTO.setNbsfzid( req.getNbsfzid() );
        hjxxCzrkjbxxbDTO.setGmsfhm( req.getGmsfhm() );
        hjxxCzrkjbxxbDTO.setQfjg( req.getQfjg() );
        hjxxCzrkjbxxbDTO.setYxqxqsrq( req.getYxqxqsrq() );
        hjxxCzrkjbxxbDTO.setYxqxjzrq( req.getYxqxjzrq() );
        hjxxCzrkjbxxbDTO.setXm( req.getXm() );
        hjxxCzrkjbxxbDTO.setCym( req.getCym() );
        hjxxCzrkjbxxbDTO.setXmpy( req.getXmpy() );
        hjxxCzrkjbxxbDTO.setCympy( req.getCympy() );
        hjxxCzrkjbxxbDTO.setXb( req.getXb() );
        hjxxCzrkjbxxbDTO.setMz( req.getMz() );
        hjxxCzrkjbxxbDTO.setCsrq( req.getCsrq() );
        hjxxCzrkjbxxbDTO.setCssj( req.getCssj() );
        hjxxCzrkjbxxbDTO.setCsdgjdq( req.getCsdgjdq() );
        hjxxCzrkjbxxbDTO.setCsdssxq( req.getCsdssxq() );
        hjxxCzrkjbxxbDTO.setCsdxz( req.getCsdxz() );
        hjxxCzrkjbxxbDTO.setDhhm( req.getDhhm() );
        hjxxCzrkjbxxbDTO.setJhryxm( req.getJhryxm() );
        hjxxCzrkjbxxbDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        hjxxCzrkjbxxbDTO.setJhryjhgx( req.getJhryjhgx() );
        hjxxCzrkjbxxbDTO.setJhrexm( req.getJhrexm() );
        hjxxCzrkjbxxbDTO.setJhregmsfhm( req.getJhregmsfhm() );
        hjxxCzrkjbxxbDTO.setJhrejhgx( req.getJhrejhgx() );
        hjxxCzrkjbxxbDTO.setFqxm( req.getFqxm() );
        hjxxCzrkjbxxbDTO.setFqgmsfhm( req.getFqgmsfhm() );
        hjxxCzrkjbxxbDTO.setMqxm( req.getMqxm() );
        hjxxCzrkjbxxbDTO.setMqgmsfhm( req.getMqgmsfhm() );
        hjxxCzrkjbxxbDTO.setPoxm( req.getPoxm() );
        hjxxCzrkjbxxbDTO.setPogmsfhm( req.getPogmsfhm() );
        hjxxCzrkjbxxbDTO.setJggjdq( req.getJggjdq() );
        hjxxCzrkjbxxbDTO.setJgssxq( req.getJgssxq() );
        hjxxCzrkjbxxbDTO.setZjxy( req.getZjxy() );
        hjxxCzrkjbxxbDTO.setWhcd( req.getWhcd() );
        hjxxCzrkjbxxbDTO.setHyzk( req.getHyzk() );
        hjxxCzrkjbxxbDTO.setByzk( req.getByzk() );
        hjxxCzrkjbxxbDTO.setSg( req.getSg() );
        hjxxCzrkjbxxbDTO.setXx( req.getXx() );
        hjxxCzrkjbxxbDTO.setZy( req.getZy() );
        hjxxCzrkjbxxbDTO.setZylb( req.getZylb() );
        hjxxCzrkjbxxbDTO.setFwcs( req.getFwcs() );
        hjxxCzrkjbxxbDTO.setXxjb( req.getXxjb() );
        hjxxCzrkjbxxbDTO.setHsql( req.getHsql() );
        hjxxCzrkjbxxbDTO.setHyql( req.getHyql() );
        hjxxCzrkjbxxbDTO.setHgjdqql( req.getHgjdqql() );
        hjxxCzrkjbxxbDTO.setHssxqql( req.getHssxqql() );
        hjxxCzrkjbxxbDTO.setHxzql( req.getHxzql() );
        hjxxCzrkjbxxbDTO.setHslbz( req.getHslbz() );
        hjxxCzrkjbxxbDTO.setHylbz( req.getHylbz() );
        hjxxCzrkjbxxbDTO.setHgjdqlbz( req.getHgjdqlbz() );
        hjxxCzrkjbxxbDTO.setHsssqlbz( req.getHsssqlbz() );
        hjxxCzrkjbxxbDTO.setHxzlbz( req.getHxzlbz() );
        hjxxCzrkjbxxbDTO.setSwrq( req.getSwrq() );
        hjxxCzrkjbxxbDTO.setSwzxlb( req.getSwzxlb() );
        hjxxCzrkjbxxbDTO.setSwzxrq( req.getSwzxrq() );
        hjxxCzrkjbxxbDTO.setQcrq( req.getQcrq() );
        hjxxCzrkjbxxbDTO.setQczxlb( req.getQczxlb() );
        hjxxCzrkjbxxbDTO.setQwdgjdq( req.getQwdgjdq() );
        hjxxCzrkjbxxbDTO.setQwdssxq( req.getQwdssxq() );
        hjxxCzrkjbxxbDTO.setQwdxz( req.getQwdxz() );
        hjxxCzrkjbxxbDTO.setCszmbh( req.getCszmbh() );
        hjxxCzrkjbxxbDTO.setCszqfrq( req.getCszqfrq() );
        hjxxCzrkjbxxbDTO.setHylb( req.getHylb() );
        hjxxCzrkjbxxbDTO.setQtssxq( req.getQtssxq() );
        hjxxCzrkjbxxbDTO.setQtzz( req.getQtzz() );
        hjxxCzrkjbxxbDTO.setRylb( req.getRylb() );
        hjxxCzrkjbxxbDTO.setHb( req.getHb() );
        hjxxCzrkjbxxbDTO.setYhzgx( req.getYhzgx() );
        hjxxCzrkjbxxbDTO.setRyzt( req.getRyzt() );
        hjxxCzrkjbxxbDTO.setRysdzt( req.getRysdzt() );
        hjxxCzrkjbxxbDTO.setLxdbid( req.getLxdbid() );
        hjxxCzrkjbxxbDTO.setBz( req.getBz() );
        hjxxCzrkjbxxbDTO.setJlbz( req.getJlbz() );
        hjxxCzrkjbxxbDTO.setYwnr( req.getYwnr() );
        hjxxCzrkjbxxbDTO.setCjhjywid( req.getCjhjywid() );
        hjxxCzrkjbxxbDTO.setCchjywid( req.getCchjywid() );
        hjxxCzrkjbxxbDTO.setQysj( req.getQysj() );
        hjxxCzrkjbxxbDTO.setJssj( req.getJssj() );
        hjxxCzrkjbxxbDTO.setCxbz( req.getCxbz() );
        hjxxCzrkjbxxbDTO.setZjlb( req.getZjlb() );
        hjxxCzrkjbxxbDTO.setJlx( req.getJlx() );
        hjxxCzrkjbxxbDTO.setMlph( req.getMlph() );
        hjxxCzrkjbxxbDTO.setMlxz( req.getMlxz() );
        hjxxCzrkjbxxbDTO.setPcs( req.getPcs() );
        hjxxCzrkjbxxbDTO.setZrq( req.getZrq() );
        hjxxCzrkjbxxbDTO.setXzjd( req.getXzjd() );
        hjxxCzrkjbxxbDTO.setJcwh( req.getJcwh() );
        hjxxCzrkjbxxbDTO.setPxh( req.getPxh() );
        hjxxCzrkjbxxbDTO.setMlpid( req.getMlpid() );
        hjxxCzrkjbxxbDTO.setSsxq( req.getSsxq() );
        hjxxCzrkjbxxbDTO.setHh( req.getHh() );
        hjxxCzrkjbxxbDTO.setHlx( req.getHlx() );
        hjxxCzrkjbxxbDTO.setHhid( req.getHhid() );
        hjxxCzrkjbxxbDTO.setBdfw( req.getBdfw() );
        hjxxCzrkjbxxbDTO.setXxqysj( req.getXxqysj() );
        hjxxCzrkjbxxbDTO.setDhhm2( req.getDhhm2() );
        hjxxCzrkjbxxbDTO.setXzdcjzt( req.getXzdcjzt() );
        hjxxCzrkjbxxbDTO.setZwyzw( req.getZwyzw() );
        hjxxCzrkjbxxbDTO.setZwyzcjg( req.getZwyzcjg() );
        hjxxCzrkjbxxbDTO.setZwezw( req.getZwezw() );
        hjxxCzrkjbxxbDTO.setZwezcjg( req.getZwezcjg() );
        hjxxCzrkjbxxbDTO.setZwcjjgdm( req.getZwcjjgdm() );
        hjxxCzrkjbxxbDTO.setSzyczkdm( req.getSzyczkdm() );
        hjxxCzrkjbxxbDTO.setX( req.getX() );
        hjxxCzrkjbxxbDTO.setM( req.getM() );
        hjxxCzrkjbxxbDTO.setJgxz( req.getJgxz() );
        hjxxCzrkjbxxbDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        hjxxCzrkjbxxbDTO.setJhryzjhm( req.getJhryzjhm() );
        hjxxCzrkjbxxbDTO.setJhrywwx( req.getJhrywwx() );
        hjxxCzrkjbxxbDTO.setJhrywwm( req.getJhrywwm() );
        hjxxCzrkjbxxbDTO.setJhrylxdh( req.getJhrylxdh() );
        hjxxCzrkjbxxbDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        hjxxCzrkjbxxbDTO.setJhrezjhm( req.getJhrezjhm() );
        hjxxCzrkjbxxbDTO.setJhrewwx( req.getJhrewwx() );
        hjxxCzrkjbxxbDTO.setJhrewwm( req.getJhrewwm() );
        hjxxCzrkjbxxbDTO.setJhrelxdh( req.getJhrelxdh() );
        hjxxCzrkjbxxbDTO.setFqcyzjdm( req.getFqcyzjdm() );
        hjxxCzrkjbxxbDTO.setFqzjhm( req.getFqzjhm() );
        hjxxCzrkjbxxbDTO.setFqwwx( req.getFqwwx() );
        hjxxCzrkjbxxbDTO.setFqwwm( req.getFqwwm() );
        hjxxCzrkjbxxbDTO.setMqcyzjdm( req.getMqcyzjdm() );
        hjxxCzrkjbxxbDTO.setMqzjhm( req.getMqzjhm() );
        hjxxCzrkjbxxbDTO.setMqwwx( req.getMqwwx() );
        hjxxCzrkjbxxbDTO.setMqwwm( req.getMqwwm() );
        hjxxCzrkjbxxbDTO.setPocyzjdm( req.getPocyzjdm() );
        hjxxCzrkjbxxbDTO.setPozjhm( req.getPozjhm() );
        hjxxCzrkjbxxbDTO.setPowwx( req.getPowwx() );
        hjxxCzrkjbxxbDTO.setPowwm( req.getPowwm() );
        hjxxCzrkjbxxbDTO.setCyzkdwbm( req.getCyzkdwbm() );
        hjxxCzrkjbxxbDTO.setCyzkdwmc( req.getCyzkdwmc() );
        hjxxCzrkjbxxbDTO.setHqyldyy( req.getHqyldyy() );
        hjxxCzrkjbxxbDTO.setSwyy( req.getSwyy() );
        hjxxCzrkjbxxbDTO.setQcqyldyy( req.getQcqyldyy() );
        hjxxCzrkjbxxbDTO.setZxsj( req.getZxsj() );
        hjxxCzrkjbxxbDTO.setGxsj( req.getGxsj() );
        hjxxCzrkjbxxbDTO.setSjgsdwdm( req.getSjgsdwdm() );
        hjxxCzrkjbxxbDTO.setSjgsdwmc( req.getSjgsdwmc() );
        hjxxCzrkjbxxbDTO.setHjddzbm( req.getHjddzbm() );
        hjxxCzrkjbxxbDTO.setHjdssxq( req.getHjdssxq() );
        hjxxCzrkjbxxbDTO.setHjdxxdz( req.getHjdxxdz() );
        hjxxCzrkjbxxbDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        hjxxCzrkjbxxbDTO.setJzddzbm( req.getJzddzbm() );
        hjxxCzrkjbxxbDTO.setJzdssxq( req.getJzdssxq() );
        hjxxCzrkjbxxbDTO.setJzdxxdz( req.getJzdxxdz() );
        hjxxCzrkjbxxbDTO.setZjdz( req.getZjdz() );
        hjxxCzrkjbxxbDTO.setHqyldyylbz( req.getHqyldyylbz() );
        hjxxCzrkjbxxbDTO.setDjsj( req.getDjsj() );
        hjxxCzrkjbxxbDTO.setDjyy( req.getDjyy() );
        hjxxCzrkjbxxbDTO.setJcdjsj( req.getJcdjsj() );
        hjxxCzrkjbxxbDTO.setHkdjid( req.getHkdjid() );
        hjxxCzrkjbxxbDTO.setDjzt( req.getDjzt() );
        hjxxCzrkjbxxbDTO.setTjyxzqh( req.getTjyxzqh() );
        hjxxCzrkjbxxbDTO.setCxsx( req.getCxsx() );

        return hjxxCzrkjbxxbDTO;
    }

    @Override
    public HjxxCzrkjbxxbDTO convertToDTO(HjxxCzrkjbxxbUpdateReq req) {
        if ( req == null ) {
            return null;
        }

        HjxxCzrkjbxxbDTO hjxxCzrkjbxxbDTO = new HjxxCzrkjbxxbDTO();

        hjxxCzrkjbxxbDTO.setRynbid( req.getRynbid() );
        hjxxCzrkjbxxbDTO.setRyid( req.getRyid() );
        hjxxCzrkjbxxbDTO.setHhnbid( req.getHhnbid() );
        hjxxCzrkjbxxbDTO.setMlpnbid( req.getMlpnbid() );
        hjxxCzrkjbxxbDTO.setZpid( req.getZpid() );
        hjxxCzrkjbxxbDTO.setNbsfzid( req.getNbsfzid() );
        hjxxCzrkjbxxbDTO.setGmsfhm( req.getGmsfhm() );
        hjxxCzrkjbxxbDTO.setQfjg( req.getQfjg() );
        hjxxCzrkjbxxbDTO.setYxqxqsrq( req.getYxqxqsrq() );
        hjxxCzrkjbxxbDTO.setYxqxjzrq( req.getYxqxjzrq() );
        hjxxCzrkjbxxbDTO.setXm( req.getXm() );
        hjxxCzrkjbxxbDTO.setCym( req.getCym() );
        hjxxCzrkjbxxbDTO.setXmpy( req.getXmpy() );
        hjxxCzrkjbxxbDTO.setCympy( req.getCympy() );
        hjxxCzrkjbxxbDTO.setXb( req.getXb() );
        hjxxCzrkjbxxbDTO.setMz( req.getMz() );
        hjxxCzrkjbxxbDTO.setCsrq( req.getCsrq() );
        hjxxCzrkjbxxbDTO.setCssj( req.getCssj() );
        hjxxCzrkjbxxbDTO.setCsdgjdq( req.getCsdgjdq() );
        hjxxCzrkjbxxbDTO.setCsdssxq( req.getCsdssxq() );
        hjxxCzrkjbxxbDTO.setCsdxz( req.getCsdxz() );
        hjxxCzrkjbxxbDTO.setDhhm( req.getDhhm() );
        hjxxCzrkjbxxbDTO.setJhryxm( req.getJhryxm() );
        hjxxCzrkjbxxbDTO.setJhrygmsfhm( req.getJhrygmsfhm() );
        hjxxCzrkjbxxbDTO.setJhryjhgx( req.getJhryjhgx() );
        hjxxCzrkjbxxbDTO.setJhrexm( req.getJhrexm() );
        hjxxCzrkjbxxbDTO.setJhregmsfhm( req.getJhregmsfhm() );
        hjxxCzrkjbxxbDTO.setJhrejhgx( req.getJhrejhgx() );
        hjxxCzrkjbxxbDTO.setFqxm( req.getFqxm() );
        hjxxCzrkjbxxbDTO.setFqgmsfhm( req.getFqgmsfhm() );
        hjxxCzrkjbxxbDTO.setMqxm( req.getMqxm() );
        hjxxCzrkjbxxbDTO.setMqgmsfhm( req.getMqgmsfhm() );
        hjxxCzrkjbxxbDTO.setPoxm( req.getPoxm() );
        hjxxCzrkjbxxbDTO.setPogmsfhm( req.getPogmsfhm() );
        hjxxCzrkjbxxbDTO.setJggjdq( req.getJggjdq() );
        hjxxCzrkjbxxbDTO.setJgssxq( req.getJgssxq() );
        hjxxCzrkjbxxbDTO.setZjxy( req.getZjxy() );
        hjxxCzrkjbxxbDTO.setWhcd( req.getWhcd() );
        hjxxCzrkjbxxbDTO.setHyzk( req.getHyzk() );
        hjxxCzrkjbxxbDTO.setByzk( req.getByzk() );
        hjxxCzrkjbxxbDTO.setSg( req.getSg() );
        hjxxCzrkjbxxbDTO.setXx( req.getXx() );
        hjxxCzrkjbxxbDTO.setZy( req.getZy() );
        hjxxCzrkjbxxbDTO.setZylb( req.getZylb() );
        hjxxCzrkjbxxbDTO.setFwcs( req.getFwcs() );
        hjxxCzrkjbxxbDTO.setXxjb( req.getXxjb() );
        hjxxCzrkjbxxbDTO.setHsql( req.getHsql() );
        hjxxCzrkjbxxbDTO.setHyql( req.getHyql() );
        hjxxCzrkjbxxbDTO.setHgjdqql( req.getHgjdqql() );
        hjxxCzrkjbxxbDTO.setHssxqql( req.getHssxqql() );
        hjxxCzrkjbxxbDTO.setHxzql( req.getHxzql() );
        hjxxCzrkjbxxbDTO.setHslbz( req.getHslbz() );
        hjxxCzrkjbxxbDTO.setHylbz( req.getHylbz() );
        hjxxCzrkjbxxbDTO.setHgjdqlbz( req.getHgjdqlbz() );
        hjxxCzrkjbxxbDTO.setHsssqlbz( req.getHsssqlbz() );
        hjxxCzrkjbxxbDTO.setHxzlbz( req.getHxzlbz() );
        hjxxCzrkjbxxbDTO.setSwrq( req.getSwrq() );
        hjxxCzrkjbxxbDTO.setSwzxlb( req.getSwzxlb() );
        hjxxCzrkjbxxbDTO.setSwzxrq( req.getSwzxrq() );
        hjxxCzrkjbxxbDTO.setQcrq( req.getQcrq() );
        hjxxCzrkjbxxbDTO.setQczxlb( req.getQczxlb() );
        hjxxCzrkjbxxbDTO.setQwdgjdq( req.getQwdgjdq() );
        hjxxCzrkjbxxbDTO.setQwdssxq( req.getQwdssxq() );
        hjxxCzrkjbxxbDTO.setQwdxz( req.getQwdxz() );
        hjxxCzrkjbxxbDTO.setCszmbh( req.getCszmbh() );
        hjxxCzrkjbxxbDTO.setCszqfrq( req.getCszqfrq() );
        hjxxCzrkjbxxbDTO.setHylb( req.getHylb() );
        hjxxCzrkjbxxbDTO.setQtssxq( req.getQtssxq() );
        hjxxCzrkjbxxbDTO.setQtzz( req.getQtzz() );
        hjxxCzrkjbxxbDTO.setRylb( req.getRylb() );
        hjxxCzrkjbxxbDTO.setHb( req.getHb() );
        hjxxCzrkjbxxbDTO.setYhzgx( req.getYhzgx() );
        hjxxCzrkjbxxbDTO.setRyzt( req.getRyzt() );
        hjxxCzrkjbxxbDTO.setRysdzt( req.getRysdzt() );
        hjxxCzrkjbxxbDTO.setLxdbid( req.getLxdbid() );
        hjxxCzrkjbxxbDTO.setBz( req.getBz() );
        hjxxCzrkjbxxbDTO.setJlbz( req.getJlbz() );
        hjxxCzrkjbxxbDTO.setYwnr( req.getYwnr() );
        hjxxCzrkjbxxbDTO.setCjhjywid( req.getCjhjywid() );
        hjxxCzrkjbxxbDTO.setCchjywid( req.getCchjywid() );
        hjxxCzrkjbxxbDTO.setQysj( req.getQysj() );
        hjxxCzrkjbxxbDTO.setJssj( req.getJssj() );
        hjxxCzrkjbxxbDTO.setCxbz( req.getCxbz() );
        hjxxCzrkjbxxbDTO.setZjlb( req.getZjlb() );
        hjxxCzrkjbxxbDTO.setJlx( req.getJlx() );
        hjxxCzrkjbxxbDTO.setMlph( req.getMlph() );
        hjxxCzrkjbxxbDTO.setMlxz( req.getMlxz() );
        hjxxCzrkjbxxbDTO.setPcs( req.getPcs() );
        hjxxCzrkjbxxbDTO.setZrq( req.getZrq() );
        hjxxCzrkjbxxbDTO.setXzjd( req.getXzjd() );
        hjxxCzrkjbxxbDTO.setJcwh( req.getJcwh() );
        hjxxCzrkjbxxbDTO.setPxh( req.getPxh() );
        hjxxCzrkjbxxbDTO.setMlpid( req.getMlpid() );
        hjxxCzrkjbxxbDTO.setSsxq( req.getSsxq() );
        hjxxCzrkjbxxbDTO.setHh( req.getHh() );
        hjxxCzrkjbxxbDTO.setHlx( req.getHlx() );
        hjxxCzrkjbxxbDTO.setHhid( req.getHhid() );
        hjxxCzrkjbxxbDTO.setBdfw( req.getBdfw() );
        hjxxCzrkjbxxbDTO.setXxqysj( req.getXxqysj() );
        hjxxCzrkjbxxbDTO.setDhhm2( req.getDhhm2() );
        hjxxCzrkjbxxbDTO.setXzdcjzt( req.getXzdcjzt() );
        hjxxCzrkjbxxbDTO.setZwyzw( req.getZwyzw() );
        hjxxCzrkjbxxbDTO.setZwyzcjg( req.getZwyzcjg() );
        hjxxCzrkjbxxbDTO.setZwezw( req.getZwezw() );
        hjxxCzrkjbxxbDTO.setZwezcjg( req.getZwezcjg() );
        hjxxCzrkjbxxbDTO.setZwcjjgdm( req.getZwcjjgdm() );
        hjxxCzrkjbxxbDTO.setSzyczkdm( req.getSzyczkdm() );
        hjxxCzrkjbxxbDTO.setX( req.getX() );
        hjxxCzrkjbxxbDTO.setM( req.getM() );
        hjxxCzrkjbxxbDTO.setJgxz( req.getJgxz() );
        hjxxCzrkjbxxbDTO.setJhrycyzjdm( req.getJhrycyzjdm() );
        hjxxCzrkjbxxbDTO.setJhryzjhm( req.getJhryzjhm() );
        hjxxCzrkjbxxbDTO.setJhrywwx( req.getJhrywwx() );
        hjxxCzrkjbxxbDTO.setJhrywwm( req.getJhrywwm() );
        hjxxCzrkjbxxbDTO.setJhrylxdh( req.getJhrylxdh() );
        hjxxCzrkjbxxbDTO.setJhrecyzjdm( req.getJhrecyzjdm() );
        hjxxCzrkjbxxbDTO.setJhrezjhm( req.getJhrezjhm() );
        hjxxCzrkjbxxbDTO.setJhrewwx( req.getJhrewwx() );
        hjxxCzrkjbxxbDTO.setJhrewwm( req.getJhrewwm() );
        hjxxCzrkjbxxbDTO.setJhrelxdh( req.getJhrelxdh() );
        hjxxCzrkjbxxbDTO.setFqcyzjdm( req.getFqcyzjdm() );
        hjxxCzrkjbxxbDTO.setFqzjhm( req.getFqzjhm() );
        hjxxCzrkjbxxbDTO.setFqwwx( req.getFqwwx() );
        hjxxCzrkjbxxbDTO.setFqwwm( req.getFqwwm() );
        hjxxCzrkjbxxbDTO.setMqcyzjdm( req.getMqcyzjdm() );
        hjxxCzrkjbxxbDTO.setMqzjhm( req.getMqzjhm() );
        hjxxCzrkjbxxbDTO.setMqwwx( req.getMqwwx() );
        hjxxCzrkjbxxbDTO.setMqwwm( req.getMqwwm() );
        hjxxCzrkjbxxbDTO.setPocyzjdm( req.getPocyzjdm() );
        hjxxCzrkjbxxbDTO.setPozjhm( req.getPozjhm() );
        hjxxCzrkjbxxbDTO.setPowwx( req.getPowwx() );
        hjxxCzrkjbxxbDTO.setPowwm( req.getPowwm() );
        hjxxCzrkjbxxbDTO.setCyzkdwbm( req.getCyzkdwbm() );
        hjxxCzrkjbxxbDTO.setCyzkdwmc( req.getCyzkdwmc() );
        hjxxCzrkjbxxbDTO.setHqyldyy( req.getHqyldyy() );
        hjxxCzrkjbxxbDTO.setSwyy( req.getSwyy() );
        hjxxCzrkjbxxbDTO.setQcqyldyy( req.getQcqyldyy() );
        hjxxCzrkjbxxbDTO.setGxsj( req.getGxsj() );
        hjxxCzrkjbxxbDTO.setSjgsdwdm( req.getSjgsdwdm() );
        hjxxCzrkjbxxbDTO.setSjgsdwmc( req.getSjgsdwmc() );
        hjxxCzrkjbxxbDTO.setHjddzbm( req.getHjddzbm() );
        hjxxCzrkjbxxbDTO.setHjdssxq( req.getHjdssxq() );
        hjxxCzrkjbxxbDTO.setHjdxxdz( req.getHjdxxdz() );
        hjxxCzrkjbxxbDTO.setHjdrhyzbs( req.getHjdrhyzbs() );
        hjxxCzrkjbxxbDTO.setJzddzbm( req.getJzddzbm() );
        hjxxCzrkjbxxbDTO.setJzdssxq( req.getJzdssxq() );
        hjxxCzrkjbxxbDTO.setJzdxxdz( req.getJzdxxdz() );
        hjxxCzrkjbxxbDTO.setZjdz( req.getZjdz() );
        hjxxCzrkjbxxbDTO.setHqyldyylbz( req.getHqyldyylbz() );
        hjxxCzrkjbxxbDTO.setDjsj( req.getDjsj() );
        hjxxCzrkjbxxbDTO.setDjyy( req.getDjyy() );
        hjxxCzrkjbxxbDTO.setJcdjsj( req.getJcdjsj() );
        hjxxCzrkjbxxbDTO.setHkdjid( req.getHkdjid() );
        hjxxCzrkjbxxbDTO.setDjzt( req.getDjzt() );
        hjxxCzrkjbxxbDTO.setTjyxzqh( req.getTjyxzqh() );
        hjxxCzrkjbxxbDTO.setCxsx( req.getCxsx() );

        return hjxxCzrkjbxxbDTO;
    }

    @Override
    public HjxxCzrkjbxxbPageResp convertToPageResp(HjxxCzrkjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxCzrkjbxxbPageResp hjxxCzrkjbxxbPageResp = new HjxxCzrkjbxxbPageResp();

        hjxxCzrkjbxxbPageResp.setRynbid( dto.getRynbid() );
        hjxxCzrkjbxxbPageResp.setRyid( dto.getRyid() );
        hjxxCzrkjbxxbPageResp.setHhnbid( dto.getHhnbid() );
        hjxxCzrkjbxxbPageResp.setMlpnbid( dto.getMlpnbid() );
        hjxxCzrkjbxxbPageResp.setZpid( dto.getZpid() );
        hjxxCzrkjbxxbPageResp.setNbsfzid( dto.getNbsfzid() );
        hjxxCzrkjbxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        hjxxCzrkjbxxbPageResp.setQfjg( dto.getQfjg() );
        hjxxCzrkjbxxbPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        hjxxCzrkjbxxbPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        hjxxCzrkjbxxbPageResp.setXm( dto.getXm() );
        hjxxCzrkjbxxbPageResp.setCym( dto.getCym() );
        hjxxCzrkjbxxbPageResp.setXmpy( dto.getXmpy() );
        hjxxCzrkjbxxbPageResp.setCympy( dto.getCympy() );
        hjxxCzrkjbxxbPageResp.setXb( dto.getXb() );
        hjxxCzrkjbxxbPageResp.setMz( dto.getMz() );
        hjxxCzrkjbxxbPageResp.setCsrq( dto.getCsrq() );
        hjxxCzrkjbxxbPageResp.setCssj( dto.getCssj() );
        hjxxCzrkjbxxbPageResp.setCsdgjdq( dto.getCsdgjdq() );
        hjxxCzrkjbxxbPageResp.setCsdssxq( dto.getCsdssxq() );
        hjxxCzrkjbxxbPageResp.setCsdxz( dto.getCsdxz() );
        hjxxCzrkjbxxbPageResp.setDhhm( dto.getDhhm() );
        hjxxCzrkjbxxbPageResp.setJhryxm( dto.getJhryxm() );
        hjxxCzrkjbxxbPageResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        hjxxCzrkjbxxbPageResp.setJhryjhgx( dto.getJhryjhgx() );
        hjxxCzrkjbxxbPageResp.setJhrexm( dto.getJhrexm() );
        hjxxCzrkjbxxbPageResp.setJhregmsfhm( dto.getJhregmsfhm() );
        hjxxCzrkjbxxbPageResp.setJhrejhgx( dto.getJhrejhgx() );
        hjxxCzrkjbxxbPageResp.setFqxm( dto.getFqxm() );
        hjxxCzrkjbxxbPageResp.setFqgmsfhm( dto.getFqgmsfhm() );
        hjxxCzrkjbxxbPageResp.setMqxm( dto.getMqxm() );
        hjxxCzrkjbxxbPageResp.setMqgmsfhm( dto.getMqgmsfhm() );
        hjxxCzrkjbxxbPageResp.setPoxm( dto.getPoxm() );
        hjxxCzrkjbxxbPageResp.setPogmsfhm( dto.getPogmsfhm() );
        hjxxCzrkjbxxbPageResp.setJggjdq( dto.getJggjdq() );
        hjxxCzrkjbxxbPageResp.setJgssxq( dto.getJgssxq() );
        hjxxCzrkjbxxbPageResp.setZjxy( dto.getZjxy() );
        hjxxCzrkjbxxbPageResp.setWhcd( dto.getWhcd() );
        hjxxCzrkjbxxbPageResp.setHyzk( dto.getHyzk() );
        hjxxCzrkjbxxbPageResp.setByzk( dto.getByzk() );
        hjxxCzrkjbxxbPageResp.setSg( dto.getSg() );
        hjxxCzrkjbxxbPageResp.setXx( dto.getXx() );
        hjxxCzrkjbxxbPageResp.setZy( dto.getZy() );
        hjxxCzrkjbxxbPageResp.setZylb( dto.getZylb() );
        hjxxCzrkjbxxbPageResp.setFwcs( dto.getFwcs() );
        hjxxCzrkjbxxbPageResp.setXxjb( dto.getXxjb() );
        hjxxCzrkjbxxbPageResp.setHsql( dto.getHsql() );
        hjxxCzrkjbxxbPageResp.setHyql( dto.getHyql() );
        hjxxCzrkjbxxbPageResp.setHgjdqql( dto.getHgjdqql() );
        hjxxCzrkjbxxbPageResp.setHssxqql( dto.getHssxqql() );
        hjxxCzrkjbxxbPageResp.setHxzql( dto.getHxzql() );
        hjxxCzrkjbxxbPageResp.setHslbz( dto.getHslbz() );
        hjxxCzrkjbxxbPageResp.setHylbz( dto.getHylbz() );
        hjxxCzrkjbxxbPageResp.setHgjdqlbz( dto.getHgjdqlbz() );
        hjxxCzrkjbxxbPageResp.setHsssqlbz( dto.getHsssqlbz() );
        hjxxCzrkjbxxbPageResp.setHxzlbz( dto.getHxzlbz() );
        hjxxCzrkjbxxbPageResp.setSwrq( dto.getSwrq() );
        hjxxCzrkjbxxbPageResp.setSwzxlb( dto.getSwzxlb() );
        hjxxCzrkjbxxbPageResp.setSwzxrq( dto.getSwzxrq() );
        hjxxCzrkjbxxbPageResp.setQcrq( dto.getQcrq() );
        hjxxCzrkjbxxbPageResp.setQczxlb( dto.getQczxlb() );
        hjxxCzrkjbxxbPageResp.setQwdgjdq( dto.getQwdgjdq() );
        hjxxCzrkjbxxbPageResp.setQwdssxq( dto.getQwdssxq() );
        hjxxCzrkjbxxbPageResp.setQwdxz( dto.getQwdxz() );
        hjxxCzrkjbxxbPageResp.setCszmbh( dto.getCszmbh() );
        hjxxCzrkjbxxbPageResp.setCszqfrq( dto.getCszqfrq() );
        hjxxCzrkjbxxbPageResp.setHylb( dto.getHylb() );
        hjxxCzrkjbxxbPageResp.setQtssxq( dto.getQtssxq() );
        hjxxCzrkjbxxbPageResp.setQtzz( dto.getQtzz() );
        hjxxCzrkjbxxbPageResp.setRylb( dto.getRylb() );
        hjxxCzrkjbxxbPageResp.setHb( dto.getHb() );
        hjxxCzrkjbxxbPageResp.setYhzgx( dto.getYhzgx() );
        hjxxCzrkjbxxbPageResp.setRyzt( dto.getRyzt() );
        hjxxCzrkjbxxbPageResp.setRysdzt( dto.getRysdzt() );
        hjxxCzrkjbxxbPageResp.setLxdbid( dto.getLxdbid() );
        hjxxCzrkjbxxbPageResp.setBz( dto.getBz() );
        hjxxCzrkjbxxbPageResp.setJlbz( dto.getJlbz() );
        hjxxCzrkjbxxbPageResp.setYwnr( dto.getYwnr() );
        hjxxCzrkjbxxbPageResp.setCjhjywid( dto.getCjhjywid() );
        hjxxCzrkjbxxbPageResp.setCchjywid( dto.getCchjywid() );
        hjxxCzrkjbxxbPageResp.setQysj( dto.getQysj() );
        hjxxCzrkjbxxbPageResp.setJssj( dto.getJssj() );
        hjxxCzrkjbxxbPageResp.setCxbz( dto.getCxbz() );
        hjxxCzrkjbxxbPageResp.setZjlb( dto.getZjlb() );
        hjxxCzrkjbxxbPageResp.setJlx( dto.getJlx() );
        hjxxCzrkjbxxbPageResp.setMlph( dto.getMlph() );
        hjxxCzrkjbxxbPageResp.setMlxz( dto.getMlxz() );
        hjxxCzrkjbxxbPageResp.setPcs( dto.getPcs() );
        hjxxCzrkjbxxbPageResp.setZrq( dto.getZrq() );
        hjxxCzrkjbxxbPageResp.setXzjd( dto.getXzjd() );
        hjxxCzrkjbxxbPageResp.setJcwh( dto.getJcwh() );
        hjxxCzrkjbxxbPageResp.setPxh( dto.getPxh() );
        hjxxCzrkjbxxbPageResp.setMlpid( dto.getMlpid() );
        hjxxCzrkjbxxbPageResp.setSsxq( dto.getSsxq() );
        hjxxCzrkjbxxbPageResp.setHh( dto.getHh() );
        hjxxCzrkjbxxbPageResp.setHlx( dto.getHlx() );
        hjxxCzrkjbxxbPageResp.setHhid( dto.getHhid() );
        hjxxCzrkjbxxbPageResp.setBdfw( dto.getBdfw() );
        hjxxCzrkjbxxbPageResp.setXxqysj( dto.getXxqysj() );
        hjxxCzrkjbxxbPageResp.setDhhm2( dto.getDhhm2() );
        hjxxCzrkjbxxbPageResp.setXzdcjzt( dto.getXzdcjzt() );
        hjxxCzrkjbxxbPageResp.setZwyzw( dto.getZwyzw() );
        hjxxCzrkjbxxbPageResp.setZwyzcjg( dto.getZwyzcjg() );
        hjxxCzrkjbxxbPageResp.setZwezw( dto.getZwezw() );
        hjxxCzrkjbxxbPageResp.setZwezcjg( dto.getZwezcjg() );
        hjxxCzrkjbxxbPageResp.setZwcjjgdm( dto.getZwcjjgdm() );
        hjxxCzrkjbxxbPageResp.setSzyczkdm( dto.getSzyczkdm() );
        hjxxCzrkjbxxbPageResp.setX( dto.getX() );
        hjxxCzrkjbxxbPageResp.setM( dto.getM() );
        hjxxCzrkjbxxbPageResp.setJgxz( dto.getJgxz() );
        hjxxCzrkjbxxbPageResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        hjxxCzrkjbxxbPageResp.setJhryzjhm( dto.getJhryzjhm() );
        hjxxCzrkjbxxbPageResp.setJhrywwx( dto.getJhrywwx() );
        hjxxCzrkjbxxbPageResp.setJhrywwm( dto.getJhrywwm() );
        hjxxCzrkjbxxbPageResp.setJhrylxdh( dto.getJhrylxdh() );
        hjxxCzrkjbxxbPageResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        hjxxCzrkjbxxbPageResp.setJhrezjhm( dto.getJhrezjhm() );
        hjxxCzrkjbxxbPageResp.setJhrewwx( dto.getJhrewwx() );
        hjxxCzrkjbxxbPageResp.setJhrewwm( dto.getJhrewwm() );
        hjxxCzrkjbxxbPageResp.setJhrelxdh( dto.getJhrelxdh() );
        hjxxCzrkjbxxbPageResp.setFqcyzjdm( dto.getFqcyzjdm() );
        hjxxCzrkjbxxbPageResp.setFqzjhm( dto.getFqzjhm() );
        hjxxCzrkjbxxbPageResp.setFqwwx( dto.getFqwwx() );
        hjxxCzrkjbxxbPageResp.setFqwwm( dto.getFqwwm() );
        hjxxCzrkjbxxbPageResp.setMqcyzjdm( dto.getMqcyzjdm() );
        hjxxCzrkjbxxbPageResp.setMqzjhm( dto.getMqzjhm() );
        hjxxCzrkjbxxbPageResp.setMqwwx( dto.getMqwwx() );
        hjxxCzrkjbxxbPageResp.setMqwwm( dto.getMqwwm() );
        hjxxCzrkjbxxbPageResp.setPocyzjdm( dto.getPocyzjdm() );
        hjxxCzrkjbxxbPageResp.setPozjhm( dto.getPozjhm() );
        hjxxCzrkjbxxbPageResp.setPowwx( dto.getPowwx() );
        hjxxCzrkjbxxbPageResp.setPowwm( dto.getPowwm() );
        hjxxCzrkjbxxbPageResp.setCyzkdwbm( dto.getCyzkdwbm() );
        hjxxCzrkjbxxbPageResp.setCyzkdwmc( dto.getCyzkdwmc() );
        hjxxCzrkjbxxbPageResp.setHqyldyy( dto.getHqyldyy() );
        hjxxCzrkjbxxbPageResp.setSwyy( dto.getSwyy() );
        hjxxCzrkjbxxbPageResp.setQcqyldyy( dto.getQcqyldyy() );
        hjxxCzrkjbxxbPageResp.setGxsj( dto.getGxsj() );
        hjxxCzrkjbxxbPageResp.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxCzrkjbxxbPageResp.setSjgsdwmc( dto.getSjgsdwmc() );
        hjxxCzrkjbxxbPageResp.setHjddzbm( dto.getHjddzbm() );
        hjxxCzrkjbxxbPageResp.setHjdssxq( dto.getHjdssxq() );
        hjxxCzrkjbxxbPageResp.setHjdxxdz( dto.getHjdxxdz() );
        hjxxCzrkjbxxbPageResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        hjxxCzrkjbxxbPageResp.setJzddzbm( dto.getJzddzbm() );
        hjxxCzrkjbxxbPageResp.setJzdssxq( dto.getJzdssxq() );
        hjxxCzrkjbxxbPageResp.setJzdxxdz( dto.getJzdxxdz() );
        hjxxCzrkjbxxbPageResp.setZjdz( dto.getZjdz() );
        hjxxCzrkjbxxbPageResp.setHqyldyylbz( dto.getHqyldyylbz() );
        hjxxCzrkjbxxbPageResp.setDjsj( dto.getDjsj() );
        hjxxCzrkjbxxbPageResp.setDjyy( dto.getDjyy() );
        hjxxCzrkjbxxbPageResp.setJcdjsj( dto.getJcdjsj() );
        hjxxCzrkjbxxbPageResp.setHkdjid( dto.getHkdjid() );
        hjxxCzrkjbxxbPageResp.setDjzt( dto.getDjzt() );
        hjxxCzrkjbxxbPageResp.setTjyxzqh( dto.getTjyxzqh() );
        hjxxCzrkjbxxbPageResp.setCxsx( dto.getCxsx() );

        return hjxxCzrkjbxxbPageResp;
    }

    @Override
    public HjxxCzrkjbxxbViewResp convertToViewResp(HjxxCzrkjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxCzrkjbxxbViewResp hjxxCzrkjbxxbViewResp = new HjxxCzrkjbxxbViewResp();

        hjxxCzrkjbxxbViewResp.setRynbid( dto.getRynbid() );
        hjxxCzrkjbxxbViewResp.setRyid( dto.getRyid() );
        hjxxCzrkjbxxbViewResp.setHhnbid( dto.getHhnbid() );
        hjxxCzrkjbxxbViewResp.setMlpnbid( dto.getMlpnbid() );
        hjxxCzrkjbxxbViewResp.setZpid( dto.getZpid() );
        hjxxCzrkjbxxbViewResp.setNbsfzid( dto.getNbsfzid() );
        hjxxCzrkjbxxbViewResp.setGmsfhm( dto.getGmsfhm() );
        hjxxCzrkjbxxbViewResp.setQfjg( dto.getQfjg() );
        hjxxCzrkjbxxbViewResp.setYxqxqsrq( dto.getYxqxqsrq() );
        hjxxCzrkjbxxbViewResp.setYxqxjzrq( dto.getYxqxjzrq() );
        hjxxCzrkjbxxbViewResp.setXm( dto.getXm() );
        hjxxCzrkjbxxbViewResp.setCym( dto.getCym() );
        hjxxCzrkjbxxbViewResp.setXmpy( dto.getXmpy() );
        hjxxCzrkjbxxbViewResp.setCympy( dto.getCympy() );
        hjxxCzrkjbxxbViewResp.setXb( dto.getXb() );
        hjxxCzrkjbxxbViewResp.setMz( dto.getMz() );
        hjxxCzrkjbxxbViewResp.setCsrq( dto.getCsrq() );
        hjxxCzrkjbxxbViewResp.setCssj( dto.getCssj() );
        hjxxCzrkjbxxbViewResp.setCsdgjdq( dto.getCsdgjdq() );
        hjxxCzrkjbxxbViewResp.setCsdssxq( dto.getCsdssxq() );
        hjxxCzrkjbxxbViewResp.setCsdxz( dto.getCsdxz() );
        hjxxCzrkjbxxbViewResp.setDhhm( dto.getDhhm() );
        hjxxCzrkjbxxbViewResp.setJhryxm( dto.getJhryxm() );
        hjxxCzrkjbxxbViewResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        hjxxCzrkjbxxbViewResp.setJhryjhgx( dto.getJhryjhgx() );
        hjxxCzrkjbxxbViewResp.setJhrexm( dto.getJhrexm() );
        hjxxCzrkjbxxbViewResp.setJhregmsfhm( dto.getJhregmsfhm() );
        hjxxCzrkjbxxbViewResp.setJhrejhgx( dto.getJhrejhgx() );
        hjxxCzrkjbxxbViewResp.setFqxm( dto.getFqxm() );
        hjxxCzrkjbxxbViewResp.setFqgmsfhm( dto.getFqgmsfhm() );
        hjxxCzrkjbxxbViewResp.setMqxm( dto.getMqxm() );
        hjxxCzrkjbxxbViewResp.setMqgmsfhm( dto.getMqgmsfhm() );
        hjxxCzrkjbxxbViewResp.setPoxm( dto.getPoxm() );
        hjxxCzrkjbxxbViewResp.setPogmsfhm( dto.getPogmsfhm() );
        hjxxCzrkjbxxbViewResp.setJggjdq( dto.getJggjdq() );
        hjxxCzrkjbxxbViewResp.setJgssxq( dto.getJgssxq() );
        hjxxCzrkjbxxbViewResp.setZjxy( dto.getZjxy() );
        hjxxCzrkjbxxbViewResp.setWhcd( dto.getWhcd() );
        hjxxCzrkjbxxbViewResp.setHyzk( dto.getHyzk() );
        hjxxCzrkjbxxbViewResp.setByzk( dto.getByzk() );
        hjxxCzrkjbxxbViewResp.setSg( dto.getSg() );
        hjxxCzrkjbxxbViewResp.setXx( dto.getXx() );
        hjxxCzrkjbxxbViewResp.setZy( dto.getZy() );
        hjxxCzrkjbxxbViewResp.setZylb( dto.getZylb() );
        hjxxCzrkjbxxbViewResp.setFwcs( dto.getFwcs() );
        hjxxCzrkjbxxbViewResp.setXxjb( dto.getXxjb() );
        hjxxCzrkjbxxbViewResp.setHsql( dto.getHsql() );
        hjxxCzrkjbxxbViewResp.setHyql( dto.getHyql() );
        hjxxCzrkjbxxbViewResp.setHgjdqql( dto.getHgjdqql() );
        hjxxCzrkjbxxbViewResp.setHssxqql( dto.getHssxqql() );
        hjxxCzrkjbxxbViewResp.setHxzql( dto.getHxzql() );
        hjxxCzrkjbxxbViewResp.setHslbz( dto.getHslbz() );
        hjxxCzrkjbxxbViewResp.setHylbz( dto.getHylbz() );
        hjxxCzrkjbxxbViewResp.setHgjdqlbz( dto.getHgjdqlbz() );
        hjxxCzrkjbxxbViewResp.setHsssqlbz( dto.getHsssqlbz() );
        hjxxCzrkjbxxbViewResp.setHxzlbz( dto.getHxzlbz() );
        hjxxCzrkjbxxbViewResp.setSwrq( dto.getSwrq() );
        hjxxCzrkjbxxbViewResp.setSwzxlb( dto.getSwzxlb() );
        hjxxCzrkjbxxbViewResp.setSwzxrq( dto.getSwzxrq() );
        hjxxCzrkjbxxbViewResp.setQcrq( dto.getQcrq() );
        hjxxCzrkjbxxbViewResp.setQczxlb( dto.getQczxlb() );
        hjxxCzrkjbxxbViewResp.setQwdgjdq( dto.getQwdgjdq() );
        hjxxCzrkjbxxbViewResp.setQwdssxq( dto.getQwdssxq() );
        hjxxCzrkjbxxbViewResp.setQwdxz( dto.getQwdxz() );
        hjxxCzrkjbxxbViewResp.setCszmbh( dto.getCszmbh() );
        hjxxCzrkjbxxbViewResp.setCszqfrq( dto.getCszqfrq() );
        hjxxCzrkjbxxbViewResp.setHylb( dto.getHylb() );
        hjxxCzrkjbxxbViewResp.setQtssxq( dto.getQtssxq() );
        hjxxCzrkjbxxbViewResp.setQtzz( dto.getQtzz() );
        hjxxCzrkjbxxbViewResp.setRylb( dto.getRylb() );
        hjxxCzrkjbxxbViewResp.setHb( dto.getHb() );
        hjxxCzrkjbxxbViewResp.setYhzgx( dto.getYhzgx() );
        hjxxCzrkjbxxbViewResp.setRyzt( dto.getRyzt() );
        hjxxCzrkjbxxbViewResp.setRysdzt( dto.getRysdzt() );
        hjxxCzrkjbxxbViewResp.setLxdbid( dto.getLxdbid() );
        hjxxCzrkjbxxbViewResp.setBz( dto.getBz() );
        hjxxCzrkjbxxbViewResp.setJlbz( dto.getJlbz() );
        hjxxCzrkjbxxbViewResp.setYwnr( dto.getYwnr() );
        hjxxCzrkjbxxbViewResp.setCjhjywid( dto.getCjhjywid() );
        hjxxCzrkjbxxbViewResp.setCchjywid( dto.getCchjywid() );
        hjxxCzrkjbxxbViewResp.setQysj( dto.getQysj() );
        hjxxCzrkjbxxbViewResp.setJssj( dto.getJssj() );
        hjxxCzrkjbxxbViewResp.setCxbz( dto.getCxbz() );
        hjxxCzrkjbxxbViewResp.setZjlb( dto.getZjlb() );
        hjxxCzrkjbxxbViewResp.setJlx( dto.getJlx() );
        hjxxCzrkjbxxbViewResp.setMlph( dto.getMlph() );
        hjxxCzrkjbxxbViewResp.setMlxz( dto.getMlxz() );
        hjxxCzrkjbxxbViewResp.setPcs( dto.getPcs() );
        hjxxCzrkjbxxbViewResp.setZrq( dto.getZrq() );
        hjxxCzrkjbxxbViewResp.setXzjd( dto.getXzjd() );
        hjxxCzrkjbxxbViewResp.setJcwh( dto.getJcwh() );
        hjxxCzrkjbxxbViewResp.setPxh( dto.getPxh() );
        hjxxCzrkjbxxbViewResp.setMlpid( dto.getMlpid() );
        hjxxCzrkjbxxbViewResp.setSsxq( dto.getSsxq() );
        hjxxCzrkjbxxbViewResp.setHh( dto.getHh() );
        hjxxCzrkjbxxbViewResp.setHlx( dto.getHlx() );
        hjxxCzrkjbxxbViewResp.setHhid( dto.getHhid() );
        hjxxCzrkjbxxbViewResp.setBdfw( dto.getBdfw() );
        hjxxCzrkjbxxbViewResp.setXxqysj( dto.getXxqysj() );
        hjxxCzrkjbxxbViewResp.setDhhm2( dto.getDhhm2() );
        hjxxCzrkjbxxbViewResp.setXzdcjzt( dto.getXzdcjzt() );
        hjxxCzrkjbxxbViewResp.setZwyzw( dto.getZwyzw() );
        hjxxCzrkjbxxbViewResp.setZwyzcjg( dto.getZwyzcjg() );
        hjxxCzrkjbxxbViewResp.setZwezw( dto.getZwezw() );
        hjxxCzrkjbxxbViewResp.setZwezcjg( dto.getZwezcjg() );
        hjxxCzrkjbxxbViewResp.setZwcjjgdm( dto.getZwcjjgdm() );
        hjxxCzrkjbxxbViewResp.setSzyczkdm( dto.getSzyczkdm() );
        hjxxCzrkjbxxbViewResp.setX( dto.getX() );
        hjxxCzrkjbxxbViewResp.setM( dto.getM() );
        hjxxCzrkjbxxbViewResp.setJgxz( dto.getJgxz() );
        hjxxCzrkjbxxbViewResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        hjxxCzrkjbxxbViewResp.setJhryzjhm( dto.getJhryzjhm() );
        hjxxCzrkjbxxbViewResp.setJhrywwx( dto.getJhrywwx() );
        hjxxCzrkjbxxbViewResp.setJhrywwm( dto.getJhrywwm() );
        hjxxCzrkjbxxbViewResp.setJhrylxdh( dto.getJhrylxdh() );
        hjxxCzrkjbxxbViewResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        hjxxCzrkjbxxbViewResp.setJhrezjhm( dto.getJhrezjhm() );
        hjxxCzrkjbxxbViewResp.setJhrewwx( dto.getJhrewwx() );
        hjxxCzrkjbxxbViewResp.setJhrewwm( dto.getJhrewwm() );
        hjxxCzrkjbxxbViewResp.setJhrelxdh( dto.getJhrelxdh() );
        hjxxCzrkjbxxbViewResp.setFqcyzjdm( dto.getFqcyzjdm() );
        hjxxCzrkjbxxbViewResp.setFqzjhm( dto.getFqzjhm() );
        hjxxCzrkjbxxbViewResp.setFqwwx( dto.getFqwwx() );
        hjxxCzrkjbxxbViewResp.setFqwwm( dto.getFqwwm() );
        hjxxCzrkjbxxbViewResp.setMqcyzjdm( dto.getMqcyzjdm() );
        hjxxCzrkjbxxbViewResp.setMqzjhm( dto.getMqzjhm() );
        hjxxCzrkjbxxbViewResp.setMqwwx( dto.getMqwwx() );
        hjxxCzrkjbxxbViewResp.setMqwwm( dto.getMqwwm() );
        hjxxCzrkjbxxbViewResp.setPocyzjdm( dto.getPocyzjdm() );
        hjxxCzrkjbxxbViewResp.setPozjhm( dto.getPozjhm() );
        hjxxCzrkjbxxbViewResp.setPowwx( dto.getPowwx() );
        hjxxCzrkjbxxbViewResp.setPowwm( dto.getPowwm() );
        hjxxCzrkjbxxbViewResp.setCyzkdwbm( dto.getCyzkdwbm() );
        hjxxCzrkjbxxbViewResp.setCyzkdwmc( dto.getCyzkdwmc() );
        hjxxCzrkjbxxbViewResp.setHqyldyy( dto.getHqyldyy() );
        hjxxCzrkjbxxbViewResp.setSwyy( dto.getSwyy() );
        hjxxCzrkjbxxbViewResp.setQcqyldyy( dto.getQcqyldyy() );
        hjxxCzrkjbxxbViewResp.setGxsj( dto.getGxsj() );
        hjxxCzrkjbxxbViewResp.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxCzrkjbxxbViewResp.setSjgsdwmc( dto.getSjgsdwmc() );
        hjxxCzrkjbxxbViewResp.setHjddzbm( dto.getHjddzbm() );
        hjxxCzrkjbxxbViewResp.setHjdssxq( dto.getHjdssxq() );
        hjxxCzrkjbxxbViewResp.setHjdxxdz( dto.getHjdxxdz() );
        hjxxCzrkjbxxbViewResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        hjxxCzrkjbxxbViewResp.setJzddzbm( dto.getJzddzbm() );
        hjxxCzrkjbxxbViewResp.setJzdssxq( dto.getJzdssxq() );
        hjxxCzrkjbxxbViewResp.setJzdxxdz( dto.getJzdxxdz() );
        hjxxCzrkjbxxbViewResp.setZjdz( dto.getZjdz() );
        hjxxCzrkjbxxbViewResp.setHqyldyylbz( dto.getHqyldyylbz() );
        hjxxCzrkjbxxbViewResp.setDjsj( dto.getDjsj() );
        hjxxCzrkjbxxbViewResp.setDjyy( dto.getDjyy() );
        hjxxCzrkjbxxbViewResp.setJcdjsj( dto.getJcdjsj() );
        hjxxCzrkjbxxbViewResp.setHkdjid( dto.getHkdjid() );
        hjxxCzrkjbxxbViewResp.setDjzt( dto.getDjzt() );
        hjxxCzrkjbxxbViewResp.setTjyxzqh( dto.getTjyxzqh() );
        hjxxCzrkjbxxbViewResp.setCxsx( dto.getCxsx() );

        return hjxxCzrkjbxxbViewResp;
    }

    @Override
    public HjxxCzrkjbxxbCreateResp convertToCreateResp(HjxxCzrkjbxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        HjxxCzrkjbxxbCreateResp hjxxCzrkjbxxbCreateResp = new HjxxCzrkjbxxbCreateResp();

        hjxxCzrkjbxxbCreateResp.setRynbid( dto.getRynbid() );
        hjxxCzrkjbxxbCreateResp.setRyid( dto.getRyid() );
        hjxxCzrkjbxxbCreateResp.setHhnbid( dto.getHhnbid() );
        hjxxCzrkjbxxbCreateResp.setMlpnbid( dto.getMlpnbid() );
        hjxxCzrkjbxxbCreateResp.setZpid( dto.getZpid() );
        hjxxCzrkjbxxbCreateResp.setNbsfzid( dto.getNbsfzid() );
        hjxxCzrkjbxxbCreateResp.setGmsfhm( dto.getGmsfhm() );
        hjxxCzrkjbxxbCreateResp.setQfjg( dto.getQfjg() );
        hjxxCzrkjbxxbCreateResp.setYxqxqsrq( dto.getYxqxqsrq() );
        hjxxCzrkjbxxbCreateResp.setYxqxjzrq( dto.getYxqxjzrq() );
        hjxxCzrkjbxxbCreateResp.setXm( dto.getXm() );
        hjxxCzrkjbxxbCreateResp.setCym( dto.getCym() );
        hjxxCzrkjbxxbCreateResp.setXmpy( dto.getXmpy() );
        hjxxCzrkjbxxbCreateResp.setCympy( dto.getCympy() );
        hjxxCzrkjbxxbCreateResp.setXb( dto.getXb() );
        hjxxCzrkjbxxbCreateResp.setMz( dto.getMz() );
        hjxxCzrkjbxxbCreateResp.setCsrq( dto.getCsrq() );
        hjxxCzrkjbxxbCreateResp.setCssj( dto.getCssj() );
        hjxxCzrkjbxxbCreateResp.setCsdgjdq( dto.getCsdgjdq() );
        hjxxCzrkjbxxbCreateResp.setCsdssxq( dto.getCsdssxq() );
        hjxxCzrkjbxxbCreateResp.setCsdxz( dto.getCsdxz() );
        hjxxCzrkjbxxbCreateResp.setDhhm( dto.getDhhm() );
        hjxxCzrkjbxxbCreateResp.setJhryxm( dto.getJhryxm() );
        hjxxCzrkjbxxbCreateResp.setJhrygmsfhm( dto.getJhrygmsfhm() );
        hjxxCzrkjbxxbCreateResp.setJhryjhgx( dto.getJhryjhgx() );
        hjxxCzrkjbxxbCreateResp.setJhrexm( dto.getJhrexm() );
        hjxxCzrkjbxxbCreateResp.setJhregmsfhm( dto.getJhregmsfhm() );
        hjxxCzrkjbxxbCreateResp.setJhrejhgx( dto.getJhrejhgx() );
        hjxxCzrkjbxxbCreateResp.setFqxm( dto.getFqxm() );
        hjxxCzrkjbxxbCreateResp.setFqgmsfhm( dto.getFqgmsfhm() );
        hjxxCzrkjbxxbCreateResp.setMqxm( dto.getMqxm() );
        hjxxCzrkjbxxbCreateResp.setMqgmsfhm( dto.getMqgmsfhm() );
        hjxxCzrkjbxxbCreateResp.setPoxm( dto.getPoxm() );
        hjxxCzrkjbxxbCreateResp.setPogmsfhm( dto.getPogmsfhm() );
        hjxxCzrkjbxxbCreateResp.setJggjdq( dto.getJggjdq() );
        hjxxCzrkjbxxbCreateResp.setJgssxq( dto.getJgssxq() );
        hjxxCzrkjbxxbCreateResp.setZjxy( dto.getZjxy() );
        hjxxCzrkjbxxbCreateResp.setWhcd( dto.getWhcd() );
        hjxxCzrkjbxxbCreateResp.setHyzk( dto.getHyzk() );
        hjxxCzrkjbxxbCreateResp.setByzk( dto.getByzk() );
        hjxxCzrkjbxxbCreateResp.setSg( dto.getSg() );
        hjxxCzrkjbxxbCreateResp.setXx( dto.getXx() );
        hjxxCzrkjbxxbCreateResp.setZy( dto.getZy() );
        hjxxCzrkjbxxbCreateResp.setZylb( dto.getZylb() );
        hjxxCzrkjbxxbCreateResp.setFwcs( dto.getFwcs() );
        hjxxCzrkjbxxbCreateResp.setXxjb( dto.getXxjb() );
        hjxxCzrkjbxxbCreateResp.setHsql( dto.getHsql() );
        hjxxCzrkjbxxbCreateResp.setHyql( dto.getHyql() );
        hjxxCzrkjbxxbCreateResp.setHgjdqql( dto.getHgjdqql() );
        hjxxCzrkjbxxbCreateResp.setHssxqql( dto.getHssxqql() );
        hjxxCzrkjbxxbCreateResp.setHxzql( dto.getHxzql() );
        hjxxCzrkjbxxbCreateResp.setHslbz( dto.getHslbz() );
        hjxxCzrkjbxxbCreateResp.setHylbz( dto.getHylbz() );
        hjxxCzrkjbxxbCreateResp.setHgjdqlbz( dto.getHgjdqlbz() );
        hjxxCzrkjbxxbCreateResp.setHsssqlbz( dto.getHsssqlbz() );
        hjxxCzrkjbxxbCreateResp.setHxzlbz( dto.getHxzlbz() );
        hjxxCzrkjbxxbCreateResp.setSwrq( dto.getSwrq() );
        hjxxCzrkjbxxbCreateResp.setSwzxlb( dto.getSwzxlb() );
        hjxxCzrkjbxxbCreateResp.setSwzxrq( dto.getSwzxrq() );
        hjxxCzrkjbxxbCreateResp.setQcrq( dto.getQcrq() );
        hjxxCzrkjbxxbCreateResp.setQczxlb( dto.getQczxlb() );
        hjxxCzrkjbxxbCreateResp.setQwdgjdq( dto.getQwdgjdq() );
        hjxxCzrkjbxxbCreateResp.setQwdssxq( dto.getQwdssxq() );
        hjxxCzrkjbxxbCreateResp.setQwdxz( dto.getQwdxz() );
        hjxxCzrkjbxxbCreateResp.setCszmbh( dto.getCszmbh() );
        hjxxCzrkjbxxbCreateResp.setCszqfrq( dto.getCszqfrq() );
        hjxxCzrkjbxxbCreateResp.setHylb( dto.getHylb() );
        hjxxCzrkjbxxbCreateResp.setQtssxq( dto.getQtssxq() );
        hjxxCzrkjbxxbCreateResp.setQtzz( dto.getQtzz() );
        hjxxCzrkjbxxbCreateResp.setRylb( dto.getRylb() );
        hjxxCzrkjbxxbCreateResp.setHb( dto.getHb() );
        hjxxCzrkjbxxbCreateResp.setYhzgx( dto.getYhzgx() );
        hjxxCzrkjbxxbCreateResp.setRyzt( dto.getRyzt() );
        hjxxCzrkjbxxbCreateResp.setRysdzt( dto.getRysdzt() );
        hjxxCzrkjbxxbCreateResp.setLxdbid( dto.getLxdbid() );
        hjxxCzrkjbxxbCreateResp.setBz( dto.getBz() );
        hjxxCzrkjbxxbCreateResp.setJlbz( dto.getJlbz() );
        hjxxCzrkjbxxbCreateResp.setYwnr( dto.getYwnr() );
        hjxxCzrkjbxxbCreateResp.setCjhjywid( dto.getCjhjywid() );
        hjxxCzrkjbxxbCreateResp.setCchjywid( dto.getCchjywid() );
        hjxxCzrkjbxxbCreateResp.setQysj( dto.getQysj() );
        hjxxCzrkjbxxbCreateResp.setJssj( dto.getJssj() );
        hjxxCzrkjbxxbCreateResp.setCxbz( dto.getCxbz() );
        hjxxCzrkjbxxbCreateResp.setZjlb( dto.getZjlb() );
        hjxxCzrkjbxxbCreateResp.setJlx( dto.getJlx() );
        hjxxCzrkjbxxbCreateResp.setMlph( dto.getMlph() );
        hjxxCzrkjbxxbCreateResp.setMlxz( dto.getMlxz() );
        hjxxCzrkjbxxbCreateResp.setPcs( dto.getPcs() );
        hjxxCzrkjbxxbCreateResp.setZrq( dto.getZrq() );
        hjxxCzrkjbxxbCreateResp.setXzjd( dto.getXzjd() );
        hjxxCzrkjbxxbCreateResp.setJcwh( dto.getJcwh() );
        hjxxCzrkjbxxbCreateResp.setPxh( dto.getPxh() );
        hjxxCzrkjbxxbCreateResp.setMlpid( dto.getMlpid() );
        hjxxCzrkjbxxbCreateResp.setSsxq( dto.getSsxq() );
        hjxxCzrkjbxxbCreateResp.setHh( dto.getHh() );
        hjxxCzrkjbxxbCreateResp.setHlx( dto.getHlx() );
        hjxxCzrkjbxxbCreateResp.setHhid( dto.getHhid() );
        hjxxCzrkjbxxbCreateResp.setBdfw( dto.getBdfw() );
        hjxxCzrkjbxxbCreateResp.setXxqysj( dto.getXxqysj() );
        hjxxCzrkjbxxbCreateResp.setDhhm2( dto.getDhhm2() );
        hjxxCzrkjbxxbCreateResp.setXzdcjzt( dto.getXzdcjzt() );
        hjxxCzrkjbxxbCreateResp.setZwyzw( dto.getZwyzw() );
        hjxxCzrkjbxxbCreateResp.setZwyzcjg( dto.getZwyzcjg() );
        hjxxCzrkjbxxbCreateResp.setZwezw( dto.getZwezw() );
        hjxxCzrkjbxxbCreateResp.setZwezcjg( dto.getZwezcjg() );
        hjxxCzrkjbxxbCreateResp.setZwcjjgdm( dto.getZwcjjgdm() );
        hjxxCzrkjbxxbCreateResp.setSzyczkdm( dto.getSzyczkdm() );
        hjxxCzrkjbxxbCreateResp.setX( dto.getX() );
        hjxxCzrkjbxxbCreateResp.setM( dto.getM() );
        hjxxCzrkjbxxbCreateResp.setJgxz( dto.getJgxz() );
        hjxxCzrkjbxxbCreateResp.setJhrycyzjdm( dto.getJhrycyzjdm() );
        hjxxCzrkjbxxbCreateResp.setJhryzjhm( dto.getJhryzjhm() );
        hjxxCzrkjbxxbCreateResp.setJhrywwx( dto.getJhrywwx() );
        hjxxCzrkjbxxbCreateResp.setJhrywwm( dto.getJhrywwm() );
        hjxxCzrkjbxxbCreateResp.setJhrylxdh( dto.getJhrylxdh() );
        hjxxCzrkjbxxbCreateResp.setJhrecyzjdm( dto.getJhrecyzjdm() );
        hjxxCzrkjbxxbCreateResp.setJhrezjhm( dto.getJhrezjhm() );
        hjxxCzrkjbxxbCreateResp.setJhrewwx( dto.getJhrewwx() );
        hjxxCzrkjbxxbCreateResp.setJhrewwm( dto.getJhrewwm() );
        hjxxCzrkjbxxbCreateResp.setJhrelxdh( dto.getJhrelxdh() );
        hjxxCzrkjbxxbCreateResp.setFqcyzjdm( dto.getFqcyzjdm() );
        hjxxCzrkjbxxbCreateResp.setFqzjhm( dto.getFqzjhm() );
        hjxxCzrkjbxxbCreateResp.setFqwwx( dto.getFqwwx() );
        hjxxCzrkjbxxbCreateResp.setFqwwm( dto.getFqwwm() );
        hjxxCzrkjbxxbCreateResp.setMqcyzjdm( dto.getMqcyzjdm() );
        hjxxCzrkjbxxbCreateResp.setMqzjhm( dto.getMqzjhm() );
        hjxxCzrkjbxxbCreateResp.setMqwwx( dto.getMqwwx() );
        hjxxCzrkjbxxbCreateResp.setMqwwm( dto.getMqwwm() );
        hjxxCzrkjbxxbCreateResp.setPocyzjdm( dto.getPocyzjdm() );
        hjxxCzrkjbxxbCreateResp.setPozjhm( dto.getPozjhm() );
        hjxxCzrkjbxxbCreateResp.setPowwx( dto.getPowwx() );
        hjxxCzrkjbxxbCreateResp.setPowwm( dto.getPowwm() );
        hjxxCzrkjbxxbCreateResp.setCyzkdwbm( dto.getCyzkdwbm() );
        hjxxCzrkjbxxbCreateResp.setCyzkdwmc( dto.getCyzkdwmc() );
        hjxxCzrkjbxxbCreateResp.setHqyldyy( dto.getHqyldyy() );
        hjxxCzrkjbxxbCreateResp.setSwyy( dto.getSwyy() );
        hjxxCzrkjbxxbCreateResp.setQcqyldyy( dto.getQcqyldyy() );
        hjxxCzrkjbxxbCreateResp.setGxsj( dto.getGxsj() );
        hjxxCzrkjbxxbCreateResp.setSjgsdwdm( dto.getSjgsdwdm() );
        hjxxCzrkjbxxbCreateResp.setSjgsdwmc( dto.getSjgsdwmc() );
        hjxxCzrkjbxxbCreateResp.setHjddzbm( dto.getHjddzbm() );
        hjxxCzrkjbxxbCreateResp.setHjdssxq( dto.getHjdssxq() );
        hjxxCzrkjbxxbCreateResp.setHjdxxdz( dto.getHjdxxdz() );
        hjxxCzrkjbxxbCreateResp.setHjdrhyzbs( dto.getHjdrhyzbs() );
        hjxxCzrkjbxxbCreateResp.setJzddzbm( dto.getJzddzbm() );
        hjxxCzrkjbxxbCreateResp.setJzdssxq( dto.getJzdssxq() );
        hjxxCzrkjbxxbCreateResp.setJzdxxdz( dto.getJzdxxdz() );
        hjxxCzrkjbxxbCreateResp.setZjdz( dto.getZjdz() );
        hjxxCzrkjbxxbCreateResp.setHqyldyylbz( dto.getHqyldyylbz() );
        hjxxCzrkjbxxbCreateResp.setDjsj( dto.getDjsj() );
        hjxxCzrkjbxxbCreateResp.setDjyy( dto.getDjyy() );
        hjxxCzrkjbxxbCreateResp.setJcdjsj( dto.getJcdjsj() );
        hjxxCzrkjbxxbCreateResp.setHkdjid( dto.getHkdjid() );
        hjxxCzrkjbxxbCreateResp.setDjzt( dto.getDjzt() );
        hjxxCzrkjbxxbCreateResp.setTjyxzqh( dto.getTjyxzqh() );
        hjxxCzrkjbxxbCreateResp.setCxsx( dto.getCxsx() );

        return hjxxCzrkjbxxbCreateResp;
    }

    @Override
    public HjxxCzrkjbxxbViewQgResp convertToResp(ZalwcxqqHjgljbxxResultVo zalwcxqqHjgljbxxResultVo) {
        if ( zalwcxqqHjgljbxxResultVo == null ) {
            return null;
        }

        HjxxCzrkjbxxbViewQgResp hjxxCzrkjbxxbViewQgResp = new HjxxCzrkjbxxbViewQgResp();

        hjxxCzrkjbxxbViewQgResp.setHjdz( zalwcxqqHjgljbxxResultVo.getHjdzQhnxxdz() );
        hjxxCzrkjbxxbViewQgResp.setGmsfhm( zalwcxqqHjgljbxxResultVo.getGmsfhm() );
        hjxxCzrkjbxxbViewQgResp.setXm( zalwcxqqHjgljbxxResultVo.getXm() );
        hjxxCzrkjbxxbViewQgResp.setBase64zp( zalwcxqqHjgljbxxResultVo.getBase64zp() );

        return hjxxCzrkjbxxbViewQgResp;
    }
}
