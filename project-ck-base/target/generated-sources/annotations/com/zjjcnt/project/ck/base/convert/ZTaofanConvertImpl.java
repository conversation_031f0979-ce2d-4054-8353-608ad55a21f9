package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZTaofanDTO;
import com.zjjcnt.project.ck.base.entity.ZTaofanDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:54+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZTaofanConvertImpl implements ZTaofanConvert {

    @Override
    public ZTaofanDTO convert(ZTaofanDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZTaofanDTO zTaofanDTO = new ZTaofanDTO();

        zTaofanDTO.setId( entity.getId() );
        zTaofanDTO.setRybh( entity.getRybh() );
        zTaofanDTO.setSfzh( entity.getSfzh() );
        zTaofanDTO.setXm( entity.getXm() );

        return zTaofanDTO;
    }

    @Override
    public ZTaofanDO convertToDO(ZTaofanDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZTaofanDO zTaofanDO = new ZTaofanDO();

        zTaofanDO.setId( dto.getId() );
        zTaofanDO.setRybh( dto.getRybh() );
        zTaofanDO.setSfzh( dto.getSfzh() );
        zTaofanDO.setXm( dto.getXm() );

        return zTaofanDO;
    }
}
