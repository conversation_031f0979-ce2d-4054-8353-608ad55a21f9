package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtZwcjsbmxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjtZwcjsbmxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjtZwcjsbmxbPageResp;
import com.zjjcnt.project.ck.base.entity.ZjtZwcjsbmxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:55+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtZwcjsbmxbConvertImpl implements ZjtZwcjsbmxbConvert {

    @Override
    public ZjtZwcjsbmxbDTO convert(ZjtZwcjsbmxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtZwcjsbmxbDTO zjtZwcjsbmxbDTO = new ZjtZwcjsbmxbDTO();

        zjtZwcjsbmxbDTO.setId( entity.getId() );
        zjtZwcjsbmxbDTO.setNbbh( entity.getNbbh() );
        zjtZwcjsbmxbDTO.setTxzlpdmx( entity.getTxzlpdmx() );
        zjtZwcjsbmxbDTO.setBdzlpdmx( entity.getBdzlpdmx() );
        zjtZwcjsbmxbDTO.setYszlpdmx( entity.getYszlpdmx() );
        zjtZwcjsbmxbDTO.setDzzdcjcs( entity.getDzzdcjcs() );
        zjtZwcjsbmxbDTO.setBlcs1( entity.getBlcs1() );
        zjtZwcjsbmxbDTO.setBlcs2( entity.getBlcs2() );
        zjtZwcjsbmxbDTO.setBlcs3( entity.getBlcs3() );
        zjtZwcjsbmxbDTO.setBlcs4( entity.getBlcs4() );
        zjtZwcjsbmxbDTO.setBlcs5( entity.getBlcs5() );

        return zjtZwcjsbmxbDTO;
    }

    @Override
    public ZjtZwcjsbmxbDO convertToDO(ZjtZwcjsbmxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjsbmxbDO zjtZwcjsbmxbDO = new ZjtZwcjsbmxbDO();

        zjtZwcjsbmxbDO.setId( dto.getId() );
        zjtZwcjsbmxbDO.setNbbh( dto.getNbbh() );
        zjtZwcjsbmxbDO.setTxzlpdmx( dto.getTxzlpdmx() );
        zjtZwcjsbmxbDO.setBdzlpdmx( dto.getBdzlpdmx() );
        zjtZwcjsbmxbDO.setYszlpdmx( dto.getYszlpdmx() );
        zjtZwcjsbmxbDO.setDzzdcjcs( dto.getDzzdcjcs() );
        zjtZwcjsbmxbDO.setBlcs1( dto.getBlcs1() );
        zjtZwcjsbmxbDO.setBlcs2( dto.getBlcs2() );
        zjtZwcjsbmxbDO.setBlcs3( dto.getBlcs3() );
        zjtZwcjsbmxbDO.setBlcs4( dto.getBlcs4() );
        zjtZwcjsbmxbDO.setBlcs5( dto.getBlcs5() );

        return zjtZwcjsbmxbDO;
    }

    @Override
    public ZjtZwcjsbmxbDTO convertToDTO(ZjtZwcjsbmxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjtZwcjsbmxbDTO zjtZwcjsbmxbDTO = new ZjtZwcjsbmxbDTO();

        zjtZwcjsbmxbDTO.setTxzlpdmx( req.getTxzlpdmx() );
        zjtZwcjsbmxbDTO.setBdzlpdmx( req.getBdzlpdmx() );
        zjtZwcjsbmxbDTO.setYszlpdmx( req.getYszlpdmx() );
        zjtZwcjsbmxbDTO.setDzzdcjcs( req.getDzzdcjcs() );
        zjtZwcjsbmxbDTO.setBlcs1( req.getBlcs1() );
        zjtZwcjsbmxbDTO.setBlcs2( req.getBlcs2() );
        zjtZwcjsbmxbDTO.setBlcs3( req.getBlcs3() );
        zjtZwcjsbmxbDTO.setBlcs4( req.getBlcs4() );
        zjtZwcjsbmxbDTO.setBlcs5( req.getBlcs5() );

        return zjtZwcjsbmxbDTO;
    }

    @Override
    public ZjtZwcjsbmxbPageResp convertToPageResp(ZjtZwcjsbmxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtZwcjsbmxbPageResp zjtZwcjsbmxbPageResp = new ZjtZwcjsbmxbPageResp();

        zjtZwcjsbmxbPageResp.setNbbh( dto.getNbbh() );
        zjtZwcjsbmxbPageResp.setTxzlpdmx( dto.getTxzlpdmx() );
        zjtZwcjsbmxbPageResp.setBdzlpdmx( dto.getBdzlpdmx() );
        zjtZwcjsbmxbPageResp.setYszlpdmx( dto.getYszlpdmx() );
        zjtZwcjsbmxbPageResp.setDzzdcjcs( dto.getDzzdcjcs() );
        zjtZwcjsbmxbPageResp.setBlcs1( dto.getBlcs1() );
        zjtZwcjsbmxbPageResp.setBlcs2( dto.getBlcs2() );
        zjtZwcjsbmxbPageResp.setBlcs3( dto.getBlcs3() );
        zjtZwcjsbmxbPageResp.setBlcs4( dto.getBlcs4() );
        zjtZwcjsbmxbPageResp.setBlcs5( dto.getBlcs5() );

        return zjtZwcjsbmxbPageResp;
    }
}
