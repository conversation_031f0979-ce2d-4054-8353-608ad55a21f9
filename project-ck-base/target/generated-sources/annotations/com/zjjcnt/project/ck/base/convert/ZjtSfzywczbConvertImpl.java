package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjtSfzywczbDTO;
import com.zjjcnt.project.ck.base.entity.ZjtSfzywczbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:58+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjtSfzywczbConvertImpl implements ZjtSfzywczbConvert {

    @Override
    public ZjtSfzywczbDTO convert(ZjtSfzywczbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjtSfzywczbDTO zjtSfzywczbDTO = new ZjtSfzywczbDTO();

        zjtSfzywczbDTO.setId( entity.getId() );
        zjtSfzywczbDTO.setZjywid( entity.getZjywid() );
        zjtSfzywczbDTO.setYwslh( entity.getYwslh() );
        zjtSfzywczbDTO.setSlh( entity.getSlh() );
        zjtSfzywczbDTO.setYwbz( entity.getYwbz() );
        zjtSfzywczbDTO.setSlzt( entity.getSlzt() );
        zjtSfzywczbDTO.setCzyid( entity.getCzyid() );
        zjtSfzywczbDTO.setCzyxm( entity.getCzyxm() );
        zjtSfzywczbDTO.setCzsj( entity.getCzsj() );
        zjtSfzywczbDTO.setCzip( entity.getCzip() );
        zjtSfzywczbDTO.setCzydwdm( entity.getCzydwdm() );
        zjtSfzywczbDTO.setCzydwmc( entity.getCzydwmc() );
        zjtSfzywczbDTO.setBz( entity.getBz() );

        return zjtSfzywczbDTO;
    }

    @Override
    public ZjtSfzywczbDO convertToDO(ZjtSfzywczbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjtSfzywczbDO zjtSfzywczbDO = new ZjtSfzywczbDO();

        zjtSfzywczbDO.setId( dto.getId() );
        zjtSfzywczbDO.setZjywid( dto.getZjywid() );
        zjtSfzywczbDO.setYwslh( dto.getYwslh() );
        zjtSfzywczbDO.setSlh( dto.getSlh() );
        zjtSfzywczbDO.setYwbz( dto.getYwbz() );
        zjtSfzywczbDO.setSlzt( dto.getSlzt() );
        zjtSfzywczbDO.setCzyid( dto.getCzyid() );
        zjtSfzywczbDO.setCzyxm( dto.getCzyxm() );
        zjtSfzywczbDO.setCzsj( dto.getCzsj() );
        zjtSfzywczbDO.setCzip( dto.getCzip() );
        zjtSfzywczbDO.setCzydwdm( dto.getCzydwdm() );
        zjtSfzywczbDO.setCzydwmc( dto.getCzydwmc() );
        zjtSfzywczbDO.setBz( dto.getBz() );

        return zjtSfzywczbDO;
    }
}
