package com.zjjcnt.project.ck.base.convert;

import com.zjjcnt.project.ck.base.dto.ZjywSlxxbDTO;
import com.zjjcnt.project.ck.base.dto.req.ZjywSlxxbPageReq;
import com.zjjcnt.project.ck.base.dto.resp.ZjywSlxxbPageResp;
import com.zjjcnt.project.ck.base.entity.ZjywSlxxbDO;
import javax.annotation.processing.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-15T15:29:57+0800",
    comments = "version: 1.6.3, compiler: javac, environment: Java 21.0.5 (Oracle Corporation)"
)
public class ZjywSlxxbConvertImpl implements ZjywSlxxbConvert {

    @Override
    public ZjywSlxxbDTO convert(ZjywSlxxbDO entity) {
        if ( entity == null ) {
            return null;
        }

        ZjywSlxxbDTO zjywSlxxbDTO = new ZjywSlxxbDTO();

        zjywSlxxbDTO.setId( entity.getId() );
        zjywSlxxbDTO.setNbslid( entity.getNbslid() );
        zjywSlxxbDTO.setSlh( entity.getSlh() );
        zjywSlxxbDTO.setRyid( entity.getRyid() );
        zjywSlxxbDTO.setRynbid( entity.getRynbid() );
        zjywSlxxbDTO.setZpid( entity.getZpid() );
        zjywSlxxbDTO.setQfjg( entity.getQfjg() );
        zjywSlxxbDTO.setYxqxqsrq( entity.getYxqxqsrq() );
        zjywSlxxbDTO.setYxqxjzrq( entity.getYxqxjzrq() );
        zjywSlxxbDTO.setZz( entity.getZz() );
        zjywSlxxbDTO.setSlyy( entity.getSlyy() );
        zjywSlxxbDTO.setZzlx( entity.getZzlx() );
        zjywSlxxbDTO.setLqfs( entity.getLqfs() );
        zjywSlxxbDTO.setSflx( entity.getSflx() );
        zjywSlxxbDTO.setSfje( entity.getSfje() );
        zjywSlxxbDTO.setSjblsh( entity.getSjblsh() );
        zjywSlxxbDTO.setSlzt( entity.getSlzt() );
        zjywSlxxbDTO.setZjywid( entity.getZjywid() );
        zjywSlxxbDTO.setCxbz( entity.getCxbz() );
        zjywSlxxbDTO.setCxsj( entity.getCxsj() );
        zjywSlxxbDTO.setCxrid( entity.getCxrid() );
        zjywSlxxbDTO.setCxzjywid( entity.getCxzjywid() );
        zjywSlxxbDTO.setTbbz( entity.getTbbz() );
        zjywSlxxbDTO.setGmsfhm( entity.getGmsfhm() );
        zjywSlxxbDTO.setNbsfzid( entity.getNbsfzid() );
        zjywSlxxbDTO.setXm( entity.getXm() );
        zjywSlxxbDTO.setXb( entity.getXb() );
        zjywSlxxbDTO.setMz( entity.getMz() );
        zjywSlxxbDTO.setCsrq( entity.getCsrq() );
        zjywSlxxbDTO.setCsdssxq( entity.getCsdssxq() );
        zjywSlxxbDTO.setMlpnbid( entity.getMlpnbid() );
        zjywSlxxbDTO.setSsxq( entity.getSsxq() );
        zjywSlxxbDTO.setJlx( entity.getJlx() );
        zjywSlxxbDTO.setMlph( entity.getMlph() );
        zjywSlxxbDTO.setMlxz( entity.getMlxz() );
        zjywSlxxbDTO.setPcs( entity.getPcs() );
        zjywSlxxbDTO.setZrq( entity.getZrq() );
        zjywSlxxbDTO.setXzjd( entity.getXzjd() );
        zjywSlxxbDTO.setJcwh( entity.getJcwh() );
        zjywSlxxbDTO.setPxh( entity.getPxh() );
        zjywSlxxbDTO.setYwbz( entity.getYwbz() );
        zjywSlxxbDTO.setCzyid( entity.getCzyid() );
        zjywSlxxbDTO.setCzsj( entity.getCzsj() );
        zjywSlxxbDTO.setDwdm( entity.getDwdm() );
        zjywSlxxbDTO.setSjrxm( entity.getSjrxm() );
        zjywSlxxbDTO.setSjrlxdh( entity.getSjrlxdh() );
        zjywSlxxbDTO.setSjryb( entity.getSjryb() );
        zjywSlxxbDTO.setSjrtxdz( entity.getSjrtxdz() );
        zjywSlxxbDTO.setZzxxcwlb( entity.getZzxxcwlb() );
        zjywSlxxbDTO.setCwms( entity.getCwms() );
        zjywSlxxbDTO.setJydw( entity.getJydw() );
        zjywSlxxbDTO.setJyrxm( entity.getJyrxm() );
        zjywSlxxbDTO.setJyrq( entity.getJyrq() );
        zjywSlxxbDTO.setCldw( entity.getCldw() );
        zjywSlxxbDTO.setClqk( entity.getClqk() );
        zjywSlxxbDTO.setClrq( entity.getClrq() );
        zjywSlxxbDTO.setZlhkzt( entity.getZlhkzt() );
        zjywSlxxbDTO.setHksj( entity.getHksj() );
        zjywSlxxbDTO.setBwbha( entity.getBwbha() );
        zjywSlxxbDTO.setBwbhb( entity.getBwbhb() );
        zjywSlxxbDTO.setShrq( entity.getShrq() );
        zjywSlxxbDTO.setStjssj( entity.getStjssj() );
        zjywSlxxbDTO.setBwbhd( entity.getBwbhd() );
        zjywSlxxbDTO.setFjpch( entity.getFjpch() );
        zjywSlxxbDTO.setRlbdid( entity.getRlbdid() );
        zjywSlxxbDTO.setRlbdbz( entity.getRlbdbz() );
        zjywSlxxbDTO.setRlbdsj( entity.getRlbdsj() );
        zjywSlxxbDTO.setZwyzw( entity.getZwyzw() );
        zjywSlxxbDTO.setZwyzcjg( entity.getZwyzcjg() );
        zjywSlxxbDTO.setZwezw( entity.getZwezw() );
        zjywSlxxbDTO.setZwezcjg( entity.getZwezcjg() );
        zjywSlxxbDTO.setZwcjjgdm( entity.getZwcjjgdm() );
        zjywSlxxbDTO.setSzyczkdm( entity.getSzyczkdm() );
        zjywSlxxbDTO.setSfzwzj( entity.getSfzwzj() );
        zjywSlxxbDTO.setDztbbz( entity.getDztbbz() );
        zjywSlxxbDTO.setDztbsj( entity.getDztbsj() );
        zjywSlxxbDTO.setDzsjbbh( entity.getDzsjbbh() );
        zjywSlxxbDTO.setSlfs( entity.getSlfs() );

        return zjywSlxxbDTO;
    }

    @Override
    public ZjywSlxxbDO convertToDO(ZjywSlxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjywSlxxbDO zjywSlxxbDO = new ZjywSlxxbDO();

        zjywSlxxbDO.setId( dto.getId() );
        zjywSlxxbDO.setNbslid( dto.getNbslid() );
        zjywSlxxbDO.setSlh( dto.getSlh() );
        zjywSlxxbDO.setRyid( dto.getRyid() );
        zjywSlxxbDO.setRynbid( dto.getRynbid() );
        zjywSlxxbDO.setZpid( dto.getZpid() );
        zjywSlxxbDO.setQfjg( dto.getQfjg() );
        zjywSlxxbDO.setYxqxqsrq( dto.getYxqxqsrq() );
        zjywSlxxbDO.setYxqxjzrq( dto.getYxqxjzrq() );
        zjywSlxxbDO.setZz( dto.getZz() );
        zjywSlxxbDO.setSlyy( dto.getSlyy() );
        zjywSlxxbDO.setZzlx( dto.getZzlx() );
        zjywSlxxbDO.setLqfs( dto.getLqfs() );
        zjywSlxxbDO.setSflx( dto.getSflx() );
        zjywSlxxbDO.setSfje( dto.getSfje() );
        zjywSlxxbDO.setSjblsh( dto.getSjblsh() );
        zjywSlxxbDO.setSlzt( dto.getSlzt() );
        zjywSlxxbDO.setZjywid( dto.getZjywid() );
        zjywSlxxbDO.setCxbz( dto.getCxbz() );
        zjywSlxxbDO.setCxsj( dto.getCxsj() );
        zjywSlxxbDO.setCxrid( dto.getCxrid() );
        zjywSlxxbDO.setCxzjywid( dto.getCxzjywid() );
        zjywSlxxbDO.setTbbz( dto.getTbbz() );
        zjywSlxxbDO.setGmsfhm( dto.getGmsfhm() );
        zjywSlxxbDO.setNbsfzid( dto.getNbsfzid() );
        zjywSlxxbDO.setXm( dto.getXm() );
        zjywSlxxbDO.setXb( dto.getXb() );
        zjywSlxxbDO.setMz( dto.getMz() );
        zjywSlxxbDO.setCsrq( dto.getCsrq() );
        zjywSlxxbDO.setCsdssxq( dto.getCsdssxq() );
        zjywSlxxbDO.setMlpnbid( dto.getMlpnbid() );
        zjywSlxxbDO.setSsxq( dto.getSsxq() );
        zjywSlxxbDO.setJlx( dto.getJlx() );
        zjywSlxxbDO.setMlph( dto.getMlph() );
        zjywSlxxbDO.setMlxz( dto.getMlxz() );
        zjywSlxxbDO.setPcs( dto.getPcs() );
        zjywSlxxbDO.setZrq( dto.getZrq() );
        zjywSlxxbDO.setXzjd( dto.getXzjd() );
        zjywSlxxbDO.setJcwh( dto.getJcwh() );
        zjywSlxxbDO.setPxh( dto.getPxh() );
        zjywSlxxbDO.setYwbz( dto.getYwbz() );
        zjywSlxxbDO.setCzyid( dto.getCzyid() );
        zjywSlxxbDO.setCzsj( dto.getCzsj() );
        zjywSlxxbDO.setDwdm( dto.getDwdm() );
        zjywSlxxbDO.setSjrxm( dto.getSjrxm() );
        zjywSlxxbDO.setSjrlxdh( dto.getSjrlxdh() );
        zjywSlxxbDO.setSjryb( dto.getSjryb() );
        zjywSlxxbDO.setSjrtxdz( dto.getSjrtxdz() );
        zjywSlxxbDO.setZzxxcwlb( dto.getZzxxcwlb() );
        zjywSlxxbDO.setCwms( dto.getCwms() );
        zjywSlxxbDO.setJydw( dto.getJydw() );
        zjywSlxxbDO.setJyrxm( dto.getJyrxm() );
        zjywSlxxbDO.setJyrq( dto.getJyrq() );
        zjywSlxxbDO.setCldw( dto.getCldw() );
        zjywSlxxbDO.setClqk( dto.getClqk() );
        zjywSlxxbDO.setClrq( dto.getClrq() );
        zjywSlxxbDO.setZlhkzt( dto.getZlhkzt() );
        zjywSlxxbDO.setHksj( dto.getHksj() );
        zjywSlxxbDO.setBwbha( dto.getBwbha() );
        zjywSlxxbDO.setBwbhb( dto.getBwbhb() );
        zjywSlxxbDO.setShrq( dto.getShrq() );
        zjywSlxxbDO.setStjssj( dto.getStjssj() );
        zjywSlxxbDO.setBwbhd( dto.getBwbhd() );
        zjywSlxxbDO.setFjpch( dto.getFjpch() );
        zjywSlxxbDO.setRlbdid( dto.getRlbdid() );
        zjywSlxxbDO.setRlbdbz( dto.getRlbdbz() );
        zjywSlxxbDO.setRlbdsj( dto.getRlbdsj() );
        zjywSlxxbDO.setZwyzw( dto.getZwyzw() );
        zjywSlxxbDO.setZwyzcjg( dto.getZwyzcjg() );
        zjywSlxxbDO.setZwezw( dto.getZwezw() );
        zjywSlxxbDO.setZwezcjg( dto.getZwezcjg() );
        zjywSlxxbDO.setZwcjjgdm( dto.getZwcjjgdm() );
        zjywSlxxbDO.setSzyczkdm( dto.getSzyczkdm() );
        zjywSlxxbDO.setSfzwzj( dto.getSfzwzj() );
        zjywSlxxbDO.setDztbbz( dto.getDztbbz() );
        zjywSlxxbDO.setDztbsj( dto.getDztbsj() );
        zjywSlxxbDO.setDzsjbbh( dto.getDzsjbbh() );
        zjywSlxxbDO.setSlfs( dto.getSlfs() );

        return zjywSlxxbDO;
    }

    @Override
    public ZjywSlxxbDTO convertToDTO(ZjywSlxxbPageReq req) {
        if ( req == null ) {
            return null;
        }

        ZjywSlxxbDTO zjywSlxxbDTO = new ZjywSlxxbDTO();

        zjywSlxxbDTO.setGmsfhm( req.getGmsfhm() );

        return zjywSlxxbDTO;
    }

    @Override
    public ZjywSlxxbPageResp convertToPageResp(ZjywSlxxbDTO dto) {
        if ( dto == null ) {
            return null;
        }

        ZjywSlxxbPageResp zjywSlxxbPageResp = new ZjywSlxxbPageResp();

        zjywSlxxbPageResp.setNbslid( dto.getNbslid() );
        zjywSlxxbPageResp.setSlh( dto.getSlh() );
        zjywSlxxbPageResp.setRyid( dto.getRyid() );
        zjywSlxxbPageResp.setRynbid( dto.getRynbid() );
        zjywSlxxbPageResp.setZpid( dto.getZpid() );
        zjywSlxxbPageResp.setQfjg( dto.getQfjg() );
        zjywSlxxbPageResp.setYxqxqsrq( dto.getYxqxqsrq() );
        zjywSlxxbPageResp.setYxqxjzrq( dto.getYxqxjzrq() );
        zjywSlxxbPageResp.setZz( dto.getZz() );
        zjywSlxxbPageResp.setSlyy( dto.getSlyy() );
        zjywSlxxbPageResp.setZzlx( dto.getZzlx() );
        zjywSlxxbPageResp.setLqfs( dto.getLqfs() );
        zjywSlxxbPageResp.setSflx( dto.getSflx() );
        zjywSlxxbPageResp.setSfje( dto.getSfje() );
        zjywSlxxbPageResp.setSjblsh( dto.getSjblsh() );
        zjywSlxxbPageResp.setSlzt( dto.getSlzt() );
        zjywSlxxbPageResp.setZjywid( dto.getZjywid() );
        zjywSlxxbPageResp.setCxbz( dto.getCxbz() );
        zjywSlxxbPageResp.setCxsj( dto.getCxsj() );
        zjywSlxxbPageResp.setCxrid( dto.getCxrid() );
        zjywSlxxbPageResp.setCxzjywid( dto.getCxzjywid() );
        zjywSlxxbPageResp.setTbbz( dto.getTbbz() );
        zjywSlxxbPageResp.setGmsfhm( dto.getGmsfhm() );
        zjywSlxxbPageResp.setNbsfzid( dto.getNbsfzid() );
        zjywSlxxbPageResp.setXm( dto.getXm() );
        zjywSlxxbPageResp.setXb( dto.getXb() );
        zjywSlxxbPageResp.setMz( dto.getMz() );
        zjywSlxxbPageResp.setCsrq( dto.getCsrq() );
        zjywSlxxbPageResp.setCsdssxq( dto.getCsdssxq() );
        zjywSlxxbPageResp.setMlpnbid( dto.getMlpnbid() );
        zjywSlxxbPageResp.setSsxq( dto.getSsxq() );
        zjywSlxxbPageResp.setJlx( dto.getJlx() );
        zjywSlxxbPageResp.setMlph( dto.getMlph() );
        zjywSlxxbPageResp.setMlxz( dto.getMlxz() );
        zjywSlxxbPageResp.setPcs( dto.getPcs() );
        zjywSlxxbPageResp.setZrq( dto.getZrq() );
        zjywSlxxbPageResp.setXzjd( dto.getXzjd() );
        zjywSlxxbPageResp.setJcwh( dto.getJcwh() );
        zjywSlxxbPageResp.setPxh( dto.getPxh() );
        zjywSlxxbPageResp.setYwbz( dto.getYwbz() );
        zjywSlxxbPageResp.setCzyid( dto.getCzyid() );
        zjywSlxxbPageResp.setCzsj( dto.getCzsj() );
        zjywSlxxbPageResp.setDwdm( dto.getDwdm() );
        zjywSlxxbPageResp.setSjrxm( dto.getSjrxm() );
        zjywSlxxbPageResp.setSjrlxdh( dto.getSjrlxdh() );
        zjywSlxxbPageResp.setSjryb( dto.getSjryb() );
        zjywSlxxbPageResp.setSjrtxdz( dto.getSjrtxdz() );
        zjywSlxxbPageResp.setZzxxcwlb( dto.getZzxxcwlb() );
        zjywSlxxbPageResp.setCwms( dto.getCwms() );
        zjywSlxxbPageResp.setJydw( dto.getJydw() );
        zjywSlxxbPageResp.setJyrxm( dto.getJyrxm() );
        zjywSlxxbPageResp.setJyrq( dto.getJyrq() );
        zjywSlxxbPageResp.setCldw( dto.getCldw() );
        zjywSlxxbPageResp.setClqk( dto.getClqk() );
        zjywSlxxbPageResp.setClrq( dto.getClrq() );
        zjywSlxxbPageResp.setZlhkzt( dto.getZlhkzt() );
        zjywSlxxbPageResp.setHksj( dto.getHksj() );
        zjywSlxxbPageResp.setBwbha( dto.getBwbha() );
        zjywSlxxbPageResp.setBwbhb( dto.getBwbhb() );
        zjywSlxxbPageResp.setShrq( dto.getShrq() );
        zjywSlxxbPageResp.setStjssj( dto.getStjssj() );
        zjywSlxxbPageResp.setBwbhd( dto.getBwbhd() );
        zjywSlxxbPageResp.setFjpch( dto.getFjpch() );
        zjywSlxxbPageResp.setRlbdid( dto.getRlbdid() );
        zjywSlxxbPageResp.setRlbdbz( dto.getRlbdbz() );
        zjywSlxxbPageResp.setRlbdsj( dto.getRlbdsj() );
        zjywSlxxbPageResp.setZwyzw( dto.getZwyzw() );
        zjywSlxxbPageResp.setZwyzcjg( dto.getZwyzcjg() );
        zjywSlxxbPageResp.setZwezw( dto.getZwezw() );
        zjywSlxxbPageResp.setZwezcjg( dto.getZwezcjg() );
        zjywSlxxbPageResp.setZwcjjgdm( dto.getZwcjjgdm() );
        zjywSlxxbPageResp.setSzyczkdm( dto.getSzyczkdm() );
        zjywSlxxbPageResp.setSfzwzj( dto.getSfzwzj() );
        zjywSlxxbPageResp.setDztbbz( dto.getDztbbz() );
        zjywSlxxbPageResp.setDztbsj( dto.getDztbsj() );
        zjywSlxxbPageResp.setDzsjbbh( dto.getDzsjbbh() );
        zjywSlxxbPageResp.setSlfs( dto.getSlfs() );

        return zjywSlxxbPageResp;
    }
}
