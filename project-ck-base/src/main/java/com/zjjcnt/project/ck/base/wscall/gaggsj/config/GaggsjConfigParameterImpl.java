package com.zjjcnt.project.ck.base.wscall.gaggsj.config;

import com.google.common.collect.Lists;
import com.zjjcnt.project.ck.base.wscall.gaggsj.convertor.GaggsjPlatformDataConvertor;

import java.util.List;

/**
 * 公安公共数据调档时的配置参数
 * 用户spring-bean的配置
 * Created by fudongwz on 2018-11-01
 */
public class GaggsjConfigParameterImpl implements GaggsjConfigParameter {

    /*********************************请求相关参数********************************/
    //全局唯一的配置ID
    private String configId;
    // 请求接口系统名称 对应 reg_id
    private String invokeServiceName;
    //请求地址.
    private String appUrl;
    //请求应用ID.
    private String appId;
    //接口服务ID.
    private String exchangeServiceId;
    //服务结构名称 对应 润乾的title标题
    private String exchangeServiceName;
    //主事项编码.
    private String powerMatters;
    // 子事项编码.
    private String subPowerMatters;


    /***************************sendData相关参数*****************************/
    //sendData转化器
    private SendDataBuilder sendDataBuilder = new MapSendDataBuilderImpl();


    /*************************转化器相关***********************************/
    //地方时数据转化器
    private GaggsjPlatformDataConvertor dataConvertor;


    /*公共页面调用配置*/
    private List<ViewInvokeParam> viewInvokeParamList = Lists.newArrayList();

    public List<ViewInvokeParam> getViewInvokeParamList() {
        return viewInvokeParamList;
    }

    public void setViewInvokeParamList(List<ViewInvokeParam> viewInvokeParamList) {
        this.viewInvokeParamList = viewInvokeParamList;
    }

    @Override
    public String getInvokeServiceName() {
        return invokeServiceName;
    }

    public void setInvokeServiceName(String invokeServiceName) {
        this.invokeServiceName = invokeServiceName;
    }

    @Override
    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    @Override
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    @Override
    public String getExchangeServiceId() {
        return exchangeServiceId;
    }

    public void setExchangeServiceId(String exchangeServiceId) {
        this.exchangeServiceId = exchangeServiceId;
    }

    @Override
    public String getPowerMatters() {
        return powerMatters;
    }

    public void setPowerMatters(String powerMatters) {
        this.powerMatters = powerMatters;
    }

    @Override
    public String getSubPowerMatters() {
        return subPowerMatters;
    }

    public void setSubPowerMatters(String subPowerMatters) {
        this.subPowerMatters = subPowerMatters;
    }


    @Override
    public SendDataBuilder getSendDataBuilder() {
        return sendDataBuilder;
    }

    public void setSendDataBuilder(SendDataBuilder sendDataBuilder) {
        this.sendDataBuilder = sendDataBuilder;
    }

    @Override
    public GaggsjPlatformDataConvertor getDataConvertor() {
        return dataConvertor;
    }

    public void setDataConvertor(GaggsjPlatformDataConvertor dataConvertor) {
        this.dataConvertor = dataConvertor;
    }

    @Override
    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }



    @Override
    public String getExchangeServiceName() {
        return exchangeServiceName;
    }

    public void setExchangeServiceName(String exchangeServiceName) {
        this.exchangeServiceName = exchangeServiceName;
    }
}
