package com.zjjcnt.project.ck.base.wscall.gaggsj.request;


import com.google.common.collect.Lists;
import com.zjjcnt.common.core.exception.ServiceException;
import com.zjjcnt.common.security.domain.CustomUserDetails;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.base.dto.JkGaggsjLwcxqqrzDTO;
import com.zjjcnt.project.ck.base.exception.CkBaseErrorCode;
import com.zjjcnt.project.ck.base.service.JkGaggsjLwcxqqrzService;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameter;
import com.zjjcnt.project.ck.base.wscall.gaggsj.exception.GaggsjInvokeException;
import com.zjjcnt.project.ck.base.wscall.gaggsj.response.GaggsjPlatformResponse;
import com.zjjcnt.project.ck.base.wscall.gaggsj.response.TokenFhResponse;
import com.zjjcnt.project.ck.core.util.SecurityUtils;
import com.zjjcnt.project.ck.sysadmin.service.FwtXtXtpzService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Map;

/**
 * 公安公共数据执行调用类
 * 用于调用指定接口
 * Created by fudongwz on 2018-11-01
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class GaggsjInvoker {

    @Autowired(required = false)
    private List<GaggsjConfigParameter> configParameterList = Lists.newArrayList();

    private final JkGaggsjLwcxqqrzService jkGaggsjLwcxqqrzService;

    private final FwtXtXtpzService xtXtpzService;
    private final RestTemplate restTemplate;

    private static String token;

    private static Long tokenExpiredTime;

    /*

       调用公共公共数据平台的指定接口

      如果存OSS，请指定 bucketName ：  oss.bucketName.gaggsj=gaggsj

     配置以下信息：

     #公安公共数据请求数据
     #请求url
     gaggsj.invoker.param.appUrl=http://127.0.0.1:8000/gayth/GaythZdpyc/gaggsj/invoker/mock/mockgaggsjresp.do
     #请求应用ID
     gaggsj.invoker.param.appId=751624B7F20C9A0AE0533C03760AFEA7

     #日志请求接口 JkByteUploadUtils工具保存blob数据
     #jk.fileobject.save.service =jkFileObjectToOSSService
     #jk.fileobject.save.service =jkFileObjectToFileService
     #jk.fileobject.save.service =jkFileObjectToDbService
     jk.fileobject.save.service =jkFileObjectToFileService
     #jkFileObjectToFileService的目录
     jk.fileobject.save.service.tofile.root =D:/zjjc/gaggsj

     #OSS配置
     oss.accessKeyId=hahahahahahahahahahahaah
     oss.accessKeySecret=hahahahahahahahahahahahahaahahhahahaha
     oss.endpoint=http://oss-cn-hangzhou.aliyuncs.com
     oss.bucketName.gaggsj=gaggsj


     GaggsjInvokeRequest的参数说明：
     一般指定exchangeServiceId即可
     如果相同的接口需要不同的配置需要指定invokeConfigId

    */
    public GaggsjInvokeResult invoke(GaggsjInvokeRequest invokeRequest, GaggsjInvokeUserInfo invokeUserInfo) {
        log.debug("开始调用【{}】调用参数 -> {}", invokeRequest.getExchangeServiceId(), getLogPrint(invokeRequest));

        //配置参数设置
        GaggsjConfigParameter configParameter = getConfigParameterByConfigId(invokeRequest.getInvokeConfigId());
        if (configParameter == null) {
            throw new GaggsjInvokeException("未找到exchangeServiceId【" + invokeRequest.getExchangeServiceId() + "】的相关配置信息");
        }

        String url = null;
        if ("[WGX33-00005095][WGX33-00006077][WGX33-00000747][WGX33-00006907][WGX33-00005931][WGX33-00000234]".indexOf(invokeRequest.getExchangeServiceId()) > 0) {
            String clientId = xtXtpzService.getCszByCslx("gaggsj.invoker.token.clientId");

            if (token == null || tokenExpiredTime == null || System.currentTimeMillis() > tokenExpiredTime) {
                getToken();
            }

            url = configParameter.getAppUrl() + "?access_token=" + token + "&client_id=" + clientId;

        } else {
            url = configParameter.getAppUrl();
        }

        log.debug("开始调用【{}】URL -> {}", invokeRequest.getExchangeServiceId(), url);

        //构造接口请求JSON
        GaggsjPlatformRequest gaggsjPlatformRequest = buildGaggsjRequest(invokeRequest, invokeUserInfo, configParameter);
        String json = JsonUtils.toJsonString(gaggsjPlatformRequest);
        log.debug("调用结束【{}】请求参数 -> {}", invokeRequest.getExchangeServiceId(), json);

        String beginTime = DateTimeUtils.now();
        //调用gaggsj接口

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<Object> httpEntity = new HttpEntity<>(json, headers);

        //调用接口
        String jsonResult = restTemplate.postForObject(url, httpEntity, String.class);
        //String jsonResult = "{\"msg\":\"成功\",\"code\":\"00\",\"data\":\"\",\"datas\":{\"root\":{\"request\":{\"header\":{\"resourceId\":null,\"records\":1,\"pageSum\":1,\"subject\":null,\"action\":\"query\",\"msgId\":\"98213e7a09a1401aae0482f12d6174dd\",\"pageSize\":1,\"resourceName\":null,\"subResourceId\":null,\"pageNum\":1,\"sendTime\":\"2021-02-26 14:22:49\"},\"body\":{\"dataList\":{\"data\":[{\"FILEURL\":\"d:/出生医学证明副页02.pdf\",\"CSYXZMBH\":\"U330039278\"}]}}}}},\"requestId\":\"844a7cbd2b3041bab883bd3553dc4639\",\"totalPage\":1,\"dataCount\":1,\"totalDataCount\":0}";
        String endTime = DateTimeUtils.now();
        if (StringUtils.isEmpty(jsonResult)) {
            throw new GaggsjInvokeException("调换接口【" + configParameter.getAppUrl() + "】结果为空");
        }

        log.debug("调用结束【{}】结果JSON -> {}", invokeRequest.getExchangeServiceId(), jsonResult);

        GaggsjInvokeResult result = new GaggsjInvokeResult();
        result.setConfigParameter(configParameter);
        result.setInvokeRequest(invokeRequest);
        result.setPlatformRequest(gaggsjPlatformRequest);
        result.setPlatformJsonResult(jsonResult);
        result.setPlatformResponse(JsonUtils.parseObject(jsonResult, GaggsjPlatformResponse.class));

        //保存联网请求日志
        JkGaggsjLwcxqqrzDTO lwcxqqrz = jkGaggsjLwcxqqrzService.save(result, invokeUserInfo, beginTime, endTime);
        result.setJkGaggsjLwcxqqrz(lwcxqqrz);

        return result;
    }


    /**
     * 根据请求的参数及配置的参数构造需要的请求JSON
     */
    private GaggsjPlatformRequest buildGaggsjRequest(GaggsjInvokeRequest invokeRequest, GaggsjInvokeUserInfo invokeUserInfo, GaggsjConfigParameter configParameter) {
        GaggsjPlatformRequest gaggsjRequestJson = new GaggsjPlatformRequest();
        /**
         * 请求应用ID. -> invokeRequest中存在appid则使用之，否则使用配置文件中appid参数
         */
        String appid;
        if (null == invokeRequest.getRequestMap().get("appid")) {
            appid = configParameter.getAppId();
        } else {
            appid = invokeRequest.getRequestMap().get("appid").toString();
        }

        gaggsjRequestJson.setAppId(appid);
        /**
         * 接口服务ID.
         */
        gaggsjRequestJson.setExchangeServiceId(configParameter.getExchangeServiceId());


        /**
         * 主事项编码.
         */
        if (StringUtils.isEmpty(invokeRequest.getPowerMatters())) {
            gaggsjRequestJson.setPowerMatters(configParameter.getPowerMatters());
        } else {
            gaggsjRequestJson.setPowerMatters(invokeRequest.getPowerMatters());
        }


        /**
         * 子事项编码.
         */
        if (StringUtils.isEmpty(invokeRequest.getSubPowerMatters())) {
            gaggsjRequestJson.setSubPowerMatters(configParameter.getSubPowerMatters());
        } else {
            gaggsjRequestJson.setSubPowerMatters(invokeRequest.getSubPowerMatters());
        }


        /*
          服务名称.
         */
        gaggsjRequestJson.setRegId(configParameter.getExchangeServiceName());

        if (invokeUserInfo != null) {

            // 请求人姓名
            gaggsjRequestJson.setRequestUserName(invokeUserInfo.getInvokeMc());

            // 请求人证件号码
            gaggsjRequestJson.setRequestUserZjhm(StringUtils.isEmpty(invokeUserInfo.getInvokeZjhm()) ? invokeUserInfo.getInvokeId() : invokeUserInfo.getInvokeZjhm());

            // 请求人所在部门编码.
            gaggsjRequestJson.setRequestDeptCode(invokeUserInfo.getInvokeSzbm());

            // 终端标识（IP地址
            gaggsjRequestJson.setTerminalId(invokeUserInfo.getInvokeHost());

            // 姓名
            gaggsjRequestJson.setUserName(invokeUserInfo.getInvokeMc());

            // 身份号码（警号）.
            gaggsjRequestJson.setUserId(StringUtils.isEmpty(invokeUserInfo.getInvokeZjhm()) ? invokeUserInfo.getInvokeId() : invokeUserInfo.getInvokeZjhm());

            // 公安机关机构代码名称.
            gaggsjRequestJson.setOrganization(invokeUserInfo.getInvokeSzbmMc());

            // 公安机关机构代码.
            gaggsjRequestJson.setOrganizationId(invokeUserInfo.getInvokeSzbm());

        } else  {
            CustomUserDetails currentUser = SecurityUtils.getCurrentUser();

            // 请求人姓名
            gaggsjRequestJson.setRequestUserName(currentUser.getName());

            // 请求人证件号码
            gaggsjRequestJson.setRequestUserZjhm(currentUser.getIdCard());

            //请求人所在部门编码.
            gaggsjRequestJson.setRequestDeptCode(currentUser.getDeptCode());

            // 终端标识（IP地址
            gaggsjRequestJson.setTerminalId(currentUser.getRemoteAddress());

            // 姓名
            gaggsjRequestJson.setUserName(currentUser.getName());

            // 身份号码（警号）.
            gaggsjRequestJson.setUserId(currentUser.getIdCard());

            // 公安机关机构代码名称
            gaggsjRequestJson.setOrganization(currentUser.getDeptName());

            // 公安机关机构代码
            gaggsjRequestJson.setOrganizationId(currentUser.getDeptCode());
        }

        // 是否办事事项，0否1是，如果是，填写主事项编码和子事项编码
        if (StringUtils.isEmpty(gaggsjRequestJson.getPowerMatters())) {
            gaggsjRequestJson.setIsMatters(0);
        } else {
            gaggsjRequestJson.setIsMatters(1);
        }

        //sendData参数
        //校验失败直接报错
        String validateResult = configParameter.getSendDataBuilder().validate(invokeRequest, configParameter);
        if (StringUtils.isNotEmpty(validateResult)) {
            throw new GaggsjInvokeException(validateResult);
        }

        Map<String, Object> sendData = configParameter.getSendDataBuilder().getSendData(invokeRequest, configParameter);
//        log.debug("构造SendData:{}", JSON.toJSONString(sendData));
        gaggsjRequestJson.setSendData(sendData);

        return gaggsjRequestJson;
    }

    /**
     * 根据GaggsjConfigParameter.ID获取接口配置信息
     */
    public GaggsjConfigParameter getConfigParameterByConfigId(String configId) {
        if (configParameterList == null || StringUtils.isEmpty(configId)) {
            return null;
        }
        for (GaggsjConfigParameter parameter : configParameterList) {
            if (parameter.getConfigId().equals(configId)) {
                return parameter;
            }
        }
        return null;
    }

    private String getLogPrint(GaggsjInvokeRequest invokeRequest) {
        return StringUtils.isEmpty(invokeRequest.getGmsfhm()) ? JsonUtils.toJsonString(invokeRequest.getRequestMap()) : invokeRequest.getGmsfhm();
    }

    //获取token信息
    private TokenFhResponse getToken() throws ServiceException {
        TokenFhResponse tokenFh = new TokenFhResponse();
        try {

            String tokenurl = xtXtpzService.getCszByCslx("gaggsj.invoker.token.url");
            String clientId = xtXtpzService.getCszByCslx("gaggsj.invoker.token.clientId");
            String clientSecret = xtXtpzService.getCszByCslx("gaggsj.invoker.token.clientSecret");
            String url = tokenurl + "?client_id=" + clientId + "&client_secret=" + clientSecret;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Object> httpEntity = new HttpEntity<>(null, headers);

            //调用接口
            String result = restTemplate.postForObject(url, httpEntity, String.class);
            log.debug("调用获取token接口返回信息：" + result);
            //解析返回结果
            if (result != null) {
                tokenFh = JsonUtils.parseObject(result, TokenFhResponse.class);
                //设置token和过期时间，token有效期为5分钟
                token = tokenFh.getToken();
                tokenExpiredTime = System.currentTimeMillis() + 5 * 60 * 1000;
                log.debug("token为{},过期时间为:{}", token, tokenExpiredTime);
            }

        } catch (Exception e) {
            throw new ServiceException(CkBaseErrorCode.GET_GAGGSJ_ERROR);
        }
        return tokenFh;
    }


}
