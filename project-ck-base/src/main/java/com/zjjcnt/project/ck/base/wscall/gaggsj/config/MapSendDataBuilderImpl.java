package com.zjjcnt.project.ck.base.wscall.gaggsj.config;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zjjcnt.common.util.JsonUtils;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvokeRequest;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 根据GaggsjInvokeRequest.requestMap构造sendData的JSON数据
 * Created by fudongwz on 2018/11/09.
 */
public class MapSendDataBuilderImpl implements SendDataBuilder {

    //其他参数
    private Map<String, Object> otherParamter;

    //必输字段，逗号分隔
    private String requiredField;

    public Map<String, Object> getOtherParamter() {
        return otherParamter;
    }

    public void setOtherParamter(Map<String, Object> otherParamter) {
        this.otherParamter = otherParamter;
    }

    @Override
    public String getRequiredField() {
        return requiredField;
    }

    public void setRequiredField(String requiredField) {
        this.requiredField = requiredField;
    }

    public Map<String, Object> getSendData(GaggsjInvokeRequest gaggsjInvokeRequest, GaggsjConfigParameter configParameter) {
        Map<String, Object> sendData = Maps.newLinkedHashMap();
        if (gaggsjInvokeRequest.getRequestMap() != null) {
            if ("WGX33-00000810".equals(configParameter.getConfigId())) {
                Map<String, Object> queryPara = Maps.newLinkedHashMap();
                queryPara.putAll(gaggsjInvokeRequest.getRequestMap());
                sendData.put("queryPara", JsonUtils.toJsonString(queryPara));
            } else {
                sendData.putAll(gaggsjInvokeRequest.getRequestMap());
            }
        }
        //其他参数
        if (this.getOtherParamter() != null && !this.getOtherParamter().isEmpty()) {
            sendData.putAll(this.getOtherParamter());
        }
        return sendData;
    }

    @Override
    public String validate(GaggsjInvokeRequest gaggsjInvokeRequest, GaggsjConfigParameter configParameter) {
        //校验必需参数
        if (StringUtils.isNotBlank(requiredField)) {
            String[] requiredFieldArray = requiredField.split(",");
            for (String field : requiredFieldArray) {
                Object value = gaggsjInvokeRequest.getRequestMap().get(field);
                if (value == null || (value instanceof String && StringUtils.isEmpty(value.toString()))) {
                    return "参数【" + field + "】不能为空";
                }
            }
        }
        return null;
    }


    //根据必输值，得到页面的录入项
    public List<ViewInvokeParam> getViewInvokeParamByRequiredField() {
        List<ViewInvokeParam> viewInvokeParamList = Lists.newArrayList();
        String reruiedField = this.getRequiredField();
        if (StringUtils.isEmpty(reruiedField)) {
            return viewInvokeParamList;
        }
        String[] requiredFieldArray = this.getRequiredField().split(",");
        for (String field : requiredFieldArray) {
            ViewInvokeParam viewInvokeParam = new ViewInvokeParam();
            viewInvokeParam.setRequired(true);
            viewInvokeParam.setField(field);
            viewInvokeParam.setChn(field);
            viewInvokeParamList.add(viewInvokeParam);
        }

        return viewInvokeParamList;
    }
}
