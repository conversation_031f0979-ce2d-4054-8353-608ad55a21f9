package com.zjjcnt.project.ck.base.web.controller;

import com.google.common.collect.Lists;
import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.project.ck.base.dto.req.GaggsjDzgxclReq;
import com.zjjcnt.project.ck.base.dto.resp.GaggsjDzgxclResp;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.GaggsjConfigParameter;
import com.zjjcnt.project.ck.base.wscall.gaggsj.config.ViewInvokeParam;
import com.zjjcnt.project.ck.base.wscall.gaggsj.convertor.StructDataToImage;
import com.zjjcnt.project.ck.base.wscall.gaggsj.exception.GaggsjInvokeException;
import com.zjjcnt.project.ck.base.wscall.gaggsj.request.GaggsjInvoker;
import com.zjjcnt.project.ck.base.wscall.gaggsj.util.GaggsjImageUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FileUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 公安公共数据controller
 *
 * <AUTHOR>
 * @date 2025-07-09 11:18:00
 */
@RequiredArgsConstructor
@Tag(name = "公安公共数据")
@RestController
@RequestMapping("/gaggsj")
public class GaggsjController {

    private final GaggsjInvoker gaggsjInvoker;

    @GetMapping("viewInvokeParam")
    @Operation(summary = "获取查询条件")
    public CommonResult<List<ViewInvokeParam>> viewInvokeParam(@RequestParam String exchangeServiceId) {
        GaggsjConfigParameter configParameter = gaggsjInvoker.getConfigParameterByConfigId(exchangeServiceId);
        if (configParameter == null) {
            throw new GaggsjInvokeException("未找到exchangeServiceId【" + exchangeServiceId + "】的相关配置信息");
        }

        //查询页面
        List<ViewInvokeParam> viewInvokeParamList = configParameter.getViewInvokeParamList();

        //如果未配置页面录入字段，则将必输项当做页面字段
        if (viewInvokeParamList == null || viewInvokeParamList.isEmpty()) {
            viewInvokeParamList = configParameter.getSendDataBuilder().getViewInvokeParamByRequiredField();
        }
        if (viewInvokeParamList == null) {
            viewInvokeParamList = Lists.newArrayList();
        }
        return CommonResult.success(viewInvokeParamList);
    }

        @PostMapping("gadzgxcl")
    @Operation(summary = "公安电子共享材料")
    public CommonResult<GaggsjDzgxclResp> gadzgxcl(@Validated @RequestBody GaggsjDzgxclReq req) throws IOException {

        GaggsjDzgxclResp resp = new GaggsjDzgxclResp();

        List<StructDataToImage> structDataToImageList = new ArrayList<>();

        byte[] pdf1 = FileUtils.readFileToByteArray(new File("/Users/<USER>/workplace/project-ck3/output-it2.pdf"));
        byte[] jpg1 = GaggsjImageUtils.convertPdf2Image(pdf1,
                0,
                1,
                0.9f);
        StructDataToImage structDataToImage1 = new StructDataToImage("1", 1, jpg1);
        structDataToImageList.add(structDataToImage1);

        byte[] bytes2 = FileUtils.readFileToByteArray(new File("/Users/<USER>/workplace/project-ck3/output-it7.pdf"));
        byte[] jpg2 = GaggsjImageUtils.convertPdf2Image(bytes2,
                0,
                1,
                0.9f);
        StructDataToImage structDataToImage2 = new StructDataToImage("2", 2, jpg2);
        structDataToImageList.add(structDataToImage2);

        resp.setStructDataToImageList(structDataToImageList);

        return CommonResult.success(resp);


//
//        GaggsjInvokeRequest gaggsjInvokeRequest = new GaggsjInvokeRequest();
//        gaggsjInvokeRequest.setExchangeServiceId(req.getExchangeServiceId());
//        gaggsjInvokeRequest.setRequestMap(req.getRequestMap());
//
//        CustomUserDetails currentUser = SecurityUtils.getCurrentUser();
//        //调用者的参数
//        GaggsjInvokeUserInfo invokeUserInfo = new GaggsjInvokeUserInfo(String.valueOf(currentUser.getUserId()),
//                currentUser.getName(), currentUser.getDeptCode(), currentUser.getRemoteAddress());
//
//        invokeUserInfo.setInvokeSys(CkBaseConstants.INVOKE_SYS_XC_WEB);
//        invokeUserInfo.setInvokeSzbmMc(currentUser.getDeptName());
//        invokeUserInfo.setInvokeZjhm(currentUser.getIdCard());
//
//
//        GaggsjInvokeResult invokeResult = gaggsjInvoker.invoke(gaggsjInvokeRequest, invokeUserInfo);
//
//        if ("WGX33-00000810".equals(gaggsjInvokeRequest.getExchangeServiceId())) {
//            //出生证副页的调用与返回需要独立处理 20210226 hubin
//            invokeResult.buildTranslateStructDataCSZFY();
//        } else if ("WGX33-00005931".equals(gaggsjInvokeRequest.getExchangeServiceId())) {
//            //殡葬服务火化信息处理
//            invokeResult.buildTranslateStructDataBzfwhhxxcx();
//
//        } else {
//            invokeResult.buildTranslateStructData();
//        }
//
//        GaggsjDzgxclResp resp = new GaggsjDzgxclResp();
//        //结构化转图片数据
//        List<StructDataToImage> structDataToImage = invokeResult.buildAllStructDataToImage();
//        resp.setStructDataToImageList(structDataToImage);
//
//        return CommonResult.success(resp);
    }
}