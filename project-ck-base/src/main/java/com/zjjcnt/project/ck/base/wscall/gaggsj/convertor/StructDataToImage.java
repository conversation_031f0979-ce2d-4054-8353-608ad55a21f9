package com.zjjcnt.project.ck.base.wscall.gaggsj.convertor;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.xml.bind.annotation.XmlTransient;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Base64;
import java.util.List;

/**
 * JPG转化结果
 * Created by fudongwz on 2018/11/02.
 */
public class StructDataToImage {
    private int index;//第几条数据

    private String logWjbh;//接口请求结果保存的文件编号JK_WJ_JB


    @JSONField(serialize = false)
    @JsonIgnore
    @XmlTransient
    private byte[] wj;//jpg结果


    private String orgiUrl;//pdf原始路径
    private String orgiDownloadWjbh;//文件下载保存编号
    private String base64wj;

    private List<Struct> structList;
    public StructDataToImage(String logWjbh, int index, byte[] wj) {
        this.wj = wj;
        this.logWjbh = logWjbh;
        this.index = index;
    }

    public List<Struct> getStructList() {
        return structList;
    }

    public void setStructList(List<Struct> structList) {
        this.structList = structList;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getLogWjbh() {
        return logWjbh;
    }

    public void setLogWjbh(String logWjbh) {
        this.logWjbh = logWjbh;
    }

    @JSONField(serialize = false)
    @com.fasterxml.jackson.annotation.JsonIgnore
    @XmlTransient
    public byte[] getWj() {
        return wj;
    }

    public void setWj(byte[] wj) {
        this.wj = wj;
    }

    public String getOrgiUrl() {
        return orgiUrl;
    }

    public void setOrgiUrl(String orgiUrl) {
        this.orgiUrl = orgiUrl;
    }

    public String getOrgiDownloadWjbh() {
        return orgiDownloadWjbh;
    }

    public void setOrgiDownloadWjbh(String orgiDownloadWjbh) {
        this.orgiDownloadWjbh = orgiDownloadWjbh;
    }

    public String getBase64wj() {
        if (StringUtils.isBlank(this.base64wj) && ArrayUtils.isNotEmpty(this.wj)) {
            try {
                this.base64wj = Base64.getEncoder().encodeToString(this.wj);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return base64wj;
    }

}
