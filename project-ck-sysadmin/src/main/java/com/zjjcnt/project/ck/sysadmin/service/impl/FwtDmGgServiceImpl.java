package com.zjjcnt.project.ck.sysadmin.service.impl;

import com.mybatisflex.core.query.QueryWrapper;
import com.zjjcnt.common.core.service.impl.AbstractBaseServiceImpl;
import com.zjjcnt.common.core.utils.ColumnUtils;
import com.zjjcnt.common.util.constant.Constants;
import com.zjjcnt.project.ck.sysadmin.convert.FwtDmGgConvert;
import com.zjjcnt.project.ck.sysadmin.dto.FwtDmGgDTO;
import com.zjjcnt.project.ck.sysadmin.entity.FwtDmGgDO;
import com.zjjcnt.project.ck.sysadmin.mapper.FwtDmGgMapper;
import com.zjjcnt.project.ck.sysadmin.service.FwtDmGgService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 公共代码ServiceImpl
 *
 * <AUTHOR>
 * @date 2024-05-13 16:43:17
 */
@Service
public class FwtDmGgServiceImpl extends AbstractBaseServiceImpl<FwtDmGgMapper, FwtDmGgDO, FwtDmGgDTO> implements FwtDmGgService {

    FwtDmGgConvert convert = FwtDmGgConvert.INSTANCE;

    @Override
    protected QueryWrapper genQueryWrapper(FwtDmGgDTO dto) {
        QueryWrapper query = QueryWrapper.create();
        query.eq(FwtDmGgDO::getDmlx, dto.getDmlx(), StringUtils.isNotEmpty(dto.getDmlx()));
        query.eq(FwtDmGgDO::getDmmc, dto.getDmmc(), StringUtils.isNotEmpty(dto.getDmmc()));
        query.eq(FwtDmGgDO::getDmpy, dto.getDmpy(), StringUtils.isNotEmpty(dto.getDmpy()));
        query.eq(FwtDmGgDO::getDmsx, dto.getDmsx(), StringUtils.isNotEmpty(dto.getDmsx()));
        query.eq(FwtDmGgDO::getYxbz, dto.getYxbz(), StringUtils.isNotEmpty(dto.getYxbz()));
        query.eq(FwtDmGgDO::getDmlxmc, ColumnUtils.encryptColumn(dto.getDmlxmc()), StringUtils.isNotEmpty(dto.getDmlxmc()));
        query.eq(FwtDmGgDO::getKzbzb, dto.getKzbzb(), StringUtils.isNotEmpty(dto.getKzbzb()));
        query.eq(FwtDmGgDO::getKzbzc, dto.getKzbzc(), StringUtils.isNotEmpty(dto.getKzbzc()));
        query.eq(FwtDmGgDO::getKzbzd, dto.getKzbzd(), StringUtils.isNotEmpty(dto.getKzbzd()));
        query.eq(FwtDmGgDO::getKzbze, dto.getKzbze(), StringUtils.isNotEmpty(dto.getKzbze()));
        query.eq(FwtDmGgDO::getKzbzf, dto.getKzbzf(), StringUtils.isNotEmpty(dto.getKzbzf()));
        query.eq(FwtDmGgDO::getKzbzg, dto.getKzbzg(), StringUtils.isNotEmpty(dto.getKzbzg()));
        return query;
    }

    @Override
    public FwtDmGgDTO convertToDTO(FwtDmGgDO fwtDmGgDO) {
        return convert.convert(fwtDmGgDO);
    }

    @Override
    public FwtDmGgDO convertToDO(FwtDmGgDTO fwtDmGgDTO) {
        return convert.convertToDO(fwtDmGgDTO);
    }

    @Override
    public List<FwtDmGgDTO> listByDmlx(String dmlx) {
        FwtDmGgDTO fwtDmGgDTO = new FwtDmGgDTO();
        fwtDmGgDTO.setDmlx(dmlx);
        fwtDmGgDTO.setYxbz(Constants.YES);
        return list(fwtDmGgDTO);
    }
}
