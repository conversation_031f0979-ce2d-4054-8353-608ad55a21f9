package com.zjjcnt.project.ck.sysadmin.convert;

import com.zjjcnt.project.ck.sysadmin.dto.FwtDmGgDTO;
import com.zjjcnt.project.ck.sysadmin.dto.resp.FwtDmGgPageResp;
import com.zjjcnt.project.ck.sysadmin.entity.FwtDmGgDO;
import org.mapstruct.InheritInverseConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
* 公共代码Convert
*
* <AUTHOR>
* @date 2024-05-13 16:43:17
*/
@Mapper
public interface FwtDmGgConvert {

    FwtDmGgConvert INSTANCE = Mappers.getMapper(FwtDmGgConvert.class);

    FwtDmGgDTO convert(FwtDmGgDO entity);

    @InheritInverseConfiguration(name="convert")
    FwtDmGgDO convertToDO(FwtDmGgDTO dto);

    FwtDmGgPageResp convertToResp(FwtDmGgDTO dto);

    List<FwtDmGgPageResp> convertToResp(List<FwtDmGgDTO> dto);

}
