package com.zjjcnt.project.ck.sysadmin.dto.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 公共代码响应对象
 *
 * <AUTHOR>
 * @date 2025-07-15 13:53:10
 */
@Data
@Schema(description = "公共代码响应对象")
public class FwtDmGgPageResp {

    @Schema(description = "")
    private String dmzm;

    @Schema(description = "")
    private String dmlx;

    @Schema(description = "")
    private String dmmc;

    @Schema(description = "")
    private String dmpy;

    @Schema(description = "")
    private String dmsx;

    @Schema(description = "")
    private String dmlxmc;

    @Schema(description = "")
    private String kzbzb;

    @Schema(description = "")
    private String kzbzc;

    @Schema(description = "")
    private String kzbzd;

    @Schema(description = "")
    private String kzbze;

    @Schema(description = "")
    private String kzbzf;

    @Schema(description = "")
    private String kzbzg;
}
