package com.zjjcnt.project.ck.sysadmin.web.controller;

import com.zjjcnt.common.core.annotation.CacheControl;
import com.zjjcnt.common.core.dict.Dictionary;
import com.zjjcnt.common.core.domain.*;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.common.util.DateTimeUtils;
import com.zjjcnt.project.ck.sysadmin.constant.SysadminConstants;
import com.zjjcnt.project.ck.sysadmin.convert.XtXtcsbConvert;
import com.zjjcnt.project.ck.sysadmin.dto.XtXtcsbDTO;
import com.zjjcnt.project.ck.sysadmin.dto.req.XtXtcsbCreateReq;
import com.zjjcnt.project.ck.sysadmin.dto.req.XtXtcsbPageReq;
import com.zjjcnt.project.ck.sysadmin.dto.req.XtXtcsbPageTypeReq;
import com.zjjcnt.project.ck.sysadmin.dto.req.XtXtcsbUpdateReq;
import com.zjjcnt.project.ck.sysadmin.dto.resp.*;
import com.zjjcnt.project.ck.sysadmin.exp.XtXtcsbExp;
import com.zjjcnt.project.ck.sysadmin.service.XtXtcsbService;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统参数表前端控制器
 *
 * <AUTHOR>
 * @date 2024-03-25 15:56:30
 */
@RequiredArgsConstructor
@Tag(name = "系统参数表")
@RestController
@RequestMapping("/xtXtcsb")
public class XtXtcsbController extends AbstractCrudController<XtXtcsbDTO> {

    private final XtXtcsbService xtXtcsbService;
    private final Dictionary dictionary;

    @Override
    protected IBaseService getService() {
        return xtXtcsbService;
    }

    @GetMapping("page")
    @Operation(summary = "查询系统参数表 N0041")
    public CommonResult<PageResult<XtXtcsbPageResp>> page(XtXtcsbPageReq req, PageParam pageParam) {
        return super.page(req, pageParam, XtXtcsbConvert.INSTANCE::convertToDTO, XtXtcsbConvert.INSTANCE::convertToPageResp);
    }

    @GetMapping("pageType")
    @Operation(summary = "查询系统参数表")
    public CommonResult<PageResult<XtXtcsbPageTypeResp>> pageType(XtXtcsbPageTypeReq req, PageParam pageParam) {
        req.setCslb(SysadminConstants.XTCS_CSLB);
        return super.page(req, pageParam, XtXtcsbConvert.INSTANCE::convertToDTO, XtXtcsbConvert.INSTANCE::convertToPageTypeResp);
    }

    @GetMapping("view")
    @Operation(summary = "查看系统参数表详情")
    public CommonResult<XtXtcsbViewResp> view(Long id) {
        return super.view(id, XtXtcsbConvert.INSTANCE::convertToViewResp);
    }

    @PostMapping("create")
    @Operation(summary = "新增系统参数表")
    public CommonResult<XtXtcsbCreateResp> create(@Validated @RequestBody XtXtcsbCreateReq req) {
        return super.create(req, XtXtcsbConvert.INSTANCE::convertToDTO, XtXtcsbConvert.INSTANCE::convertToCreateResp);
    }

    @PostMapping("update")
    @Operation(summary = "编辑系统参数表")
    public CommonResult<Boolean> update(@Validated @RequestBody XtXtcsbUpdateReq req) {
        return super.update(req, XtXtcsbConvert.INSTANCE::convertToDTO);
    }

    @DeleteMapping(value = "delete")
    @Operation(summary = "删除系统参数表")
    public CommonResult<Boolean> delete(@RequestBody Long[] id) {
        return super.delete(id);
    }

    @GetMapping(value = "/column")
    @Operation(summary = "查询可导出列")
    public CommonResult<List<ExportColumn>> column() {
        List<ExportColumn> exportColumns = getColumns(XtXtcsbExp.class);
        return CommonResult.success(exportColumns);
    }

    @GetMapping("export")
    @Operation(summary = "导出系统参数表")
    public void export(XtXtcsbPageReq req, String[] columns, PageParam pageParam, HttpServletResponse response) {
        String filename = "系统参数表" + DateTimeUtils.now() + ".xlsx";
        super.export(req, columns, pageParam, filename, XtXtcsbExp.class, XtXtcsbConvert.INSTANCE::convertToDTO, XtXtcsbConvert.INSTANCE::convertToExp, response);
    }

    @CacheControl(maxAge = 3600)
    @Operation(summary = "根据类型获取字典列表")
    @GetMapping(value = "/listByType")
    public CommonResult<List<TextValue>> listByType(@RequestParam String type) {
        Map<String, String> codeMap = Dictionary.get(type);
        List<TextValue> list = codeMap.keySet().stream().map(s -> new TextValue(codeMap.get(s), s)).collect(Collectors.toList());
        return CommonResult.success(list);
    }

    @GetMapping("listByCslb")
    @Operation(summary = "根据参数类别获取")
    public CommonResult<List<XtXtcsbListByCslbResp>> listByCslb(@RequestParam String cslb) {
        List<XtXtcsbDTO> xtXtcsbDTOList = xtXtcsbService.listByCslb(cslb);
        List<XtXtcsbListByCslbResp> result = xtXtcsbDTOList.stream().map(XtXtcsbConvert.INSTANCE::convertToListByCslbResp).collect(Collectors.toList());
        return CommonResult.success(result);
    }

    @Operation(summary = "重载字典")
    @GetMapping(value = "/reload")
    public CommonResult<Boolean> reload() {
        dictionary.loadAll();
        return CommonResult.success(true);
    }

}
