package com.zjjcnt.project.ck.sysadmin.web.controller;

import com.zjjcnt.common.core.domain.CommonResult;
import com.zjjcnt.common.core.service.IBaseService;
import com.zjjcnt.common.core.web.controller.AbstractCrudController;
import com.zjjcnt.project.ck.sysadmin.convert.FwtDmGgConvert;
import com.zjjcnt.project.ck.sysadmin.dto.FwtDmGgDTO;
import com.zjjcnt.project.ck.sysadmin.dto.resp.FwtDmGgPageResp;
import com.zjjcnt.project.ck.sysadmin.service.FwtDmGgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 公共代码表前端控制器
 *
 * <AUTHOR>
 * @date 2025-07-215 13:56:30
 */
@RequiredArgsConstructor
@Tag(name = "公共代码表")
@RestController
@RequestMapping("/fwtDmGg")
public class FwtDmGgController extends AbstractCrudController<FwtDmGgDTO> {

    private final FwtDmGgService fwtDmGgService;

    @Override
    protected IBaseService<FwtDmGgDTO> getService() {
        return fwtDmGgService;
    }

    @GetMapping("listByDmlx")
    @Operation(summary = "根据代码类别获取")
    public CommonResult<List<FwtDmGgPageResp>> listByDmlx(@RequestParam String dmlx) {
        List<FwtDmGgDTO> fwtDmGgList = fwtDmGgService.listByDmlx(dmlx);
        return CommonResult.success(FwtDmGgConvert.INSTANCE.convertToResp(fwtDmGgList));
    }

}
